{"sources": [{"file": "D:/ESP32_Projects/hello_world/build/CMakeFiles/bootloader"}, {"file": "D:/ESP32_Projects/hello_world/build/CMakeFiles/bootloader.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/ESP32_Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}