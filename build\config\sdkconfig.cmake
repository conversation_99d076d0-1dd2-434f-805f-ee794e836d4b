#
                # Automatically generated file. DO NOT EDIT.
                # Espressif IoT Development Framework (ESP-IDF) Configuration cmake include file
                #
set(CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "Not determined")
set(CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED "Not determined")
set(CONFIG_SOC_DPORT_WORKAROUND "Not determined")
set(CONFIG_SOC_CAPS_ECO_VER_MAX "301")
set(CONFIG_SOC_ADC_SUPPORTED "y")
set(CONFIG_SOC_DAC_SUPPORTED "y")
set(CONFIG_SOC_UART_SUPPORTED "y")
set(CONFIG_SOC_MCPWM_SUPPORTED "y")
set(CONFIG_SOC_GPTIMER_SUPPORTED "y")
set(CONFIG_SOC_SDMMC_HOST_SUPPORTED "y")
set(CONFIG_SOC_BT_SUPPORTED "y")
set(CONFIG_SOC_PCNT_SUPPORTED "y")
set(CONFIG_SOC_PHY_SUPPORTED "y")
set(CONFIG_SOC_WIFI_SUPPORTED "y")
set(CONFIG_SOC_SDIO_SLAVE_SUPPORTED "y")
set(CONFIG_SOC_TWAI_SUPPORTED "y")
set(CONFIG_SOC_EFUSE_SUPPORTED "y")
set(CONFIG_SOC_EMAC_SUPPORTED "y")
set(CONFIG_SOC_ULP_SUPPORTED "y")
set(CONFIG_SOC_CCOMP_TIMER_SUPPORTED "y")
set(CONFIG_SOC_RTC_FAST_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_MEM_SUPPORTED "y")
set(CONFIG_SOC_I2S_SUPPORTED "y")
set(CONFIG_SOC_RMT_SUPPORTED "y")
set(CONFIG_SOC_SDM_SUPPORTED "y")
set(CONFIG_SOC_GPSPI_SUPPORTED "y")
set(CONFIG_SOC_LEDC_SUPPORTED "y")
set(CONFIG_SOC_I2C_SUPPORTED "y")
set(CONFIG_SOC_SUPPORT_COEXISTENCE "y")
set(CONFIG_SOC_AES_SUPPORTED "y")
set(CONFIG_SOC_MPI_SUPPORTED "y")
set(CONFIG_SOC_SHA_SUPPORTED "y")
set(CONFIG_SOC_FLASH_ENC_SUPPORTED "y")
set(CONFIG_SOC_SECURE_BOOT_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_BOD_SUPPORTED "y")
set(CONFIG_SOC_ULP_FSM_SUPPORTED "y")
set(CONFIG_SOC_CLK_TREE_SUPPORTED "y")
set(CONFIG_SOC_MPU_SUPPORTED "y")
set(CONFIG_SOC_WDT_SUPPORTED "y")
set(CONFIG_SOC_SPI_FLASH_SUPPORTED "y")
set(CONFIG_SOC_RNG_SUPPORTED "y")
set(CONFIG_SOC_LIGHT_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_DEEP_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT "y")
set(CONFIG_SOC_PM_SUPPORTED "y")
set(CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL "5")
set(CONFIG_SOC_XTAL_SUPPORT_26M "y")
set(CONFIG_SOC_XTAL_SUPPORT_40M "y")
set(CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT "y")
set(CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DMA_SUPPORTED "y")
set(CONFIG_SOC_ADC_PERIPH_NUM "2")
set(CONFIG_SOC_ADC_MAX_CHANNEL_NUM "10")
set(CONFIG_SOC_ADC_ATTEN_NUM "4")
set(CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM "2")
set(CONFIG_SOC_ADC_PATT_LEN_MAX "16")
set(CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH "9")
set(CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_RESULT_BYTES "2")
set(CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV "4")
set(CONFIG_SOC_ADC_DIGI_MONITOR_NUM "0")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH "2")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW "20")
set(CONFIG_SOC_ADC_RTC_MIN_BITWIDTH "9")
set(CONFIG_SOC_ADC_RTC_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_SHARED_POWER "y")
set(CONFIG_SOC_SHARED_IDCACHE_SUPPORTED "y")
set(CONFIG_SOC_IDCACHE_PER_CORE "y")
set(CONFIG_SOC_CPU_CORES_NUM "2")
set(CONFIG_SOC_CPU_INTR_NUM "32")
set(CONFIG_SOC_CPU_HAS_FPU "y")
set(CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES "y")
set(CONFIG_SOC_CPU_BREAKPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE "64")
set(CONFIG_SOC_DAC_CHAN_NUM "2")
set(CONFIG_SOC_DAC_RESOLUTION "8")
set(CONFIG_SOC_DAC_DMA_16BIT_ALIGN "y")
set(CONFIG_SOC_GPIO_PORT "1")
set(CONFIG_SOC_GPIO_PIN_COUNT "40")
set(CONFIG_SOC_GPIO_VALID_GPIO_MASK "0xffffffffff")
set(CONFIG_SOC_GPIO_IN_RANGE_MAX "39")
set(CONFIG_SOC_GPIO_OUT_RANGE_MAX "33")
set(CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK "0xef0fea")
set(CONFIG_SOC_GPIO_CLOCKOUT_BY_IO_MUX "y")
set(CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM "3")
set(CONFIG_SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP "y")
set(CONFIG_SOC_I2C_NUM "2")
set(CONFIG_SOC_HP_I2C_NUM "2")
set(CONFIG_SOC_I2C_FIFO_LEN "32")
set(CONFIG_SOC_I2C_CMD_REG_NUM "16")
set(CONFIG_SOC_I2C_SUPPORT_SLAVE "y")
set(CONFIG_SOC_I2C_SUPPORT_APB "y")
set(CONFIG_SOC_I2C_STOP_INDEPENDENT "y")
set(CONFIG_SOC_I2S_NUM "2")
set(CONFIG_SOC_I2S_HW_VERSION_1 "y")
set(CONFIG_SOC_I2S_SUPPORTS_APLL "y")
set(CONFIG_SOC_I2S_SUPPORTS_PLL_F160M "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_TX "y")
set(CONFIG_SOC_I2S_PDM_MAX_TX_LINES "1")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_RX "y")
set(CONFIG_SOC_I2S_PDM_MAX_RX_LINES "1")
set(CONFIG_SOC_I2S_SUPPORTS_ADC_DAC "y")
set(CONFIG_SOC_I2S_SUPPORTS_ADC "y")
set(CONFIG_SOC_I2S_SUPPORTS_DAC "y")
set(CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA "y")
set(CONFIG_SOC_I2S_MAX_DATA_WIDTH "24")
set(CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD "y")
set(CONFIG_SOC_I2S_LCD_I80_VARIANT "y")
set(CONFIG_SOC_LCD_I80_SUPPORTED "y")
set(CONFIG_SOC_LCD_I80_BUSES "2")
set(CONFIG_SOC_LCD_I80_BUS_WIDTH "24")
set(CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX "y")
set(CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK "y")
set(CONFIG_SOC_LEDC_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_LEDC_SUPPORT_HS_MODE "y")
set(CONFIG_SOC_LEDC_CHANNEL_NUM "8")
set(CONFIG_SOC_LEDC_TIMER_BIT_WIDTH "20")
set(CONFIG_SOC_MCPWM_GROUPS "2")
set(CONFIG_SOC_MCPWM_TIMERS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP "y")
set(CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER "3")
set(CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP "3")
set(CONFIG_SOC_MMU_PERIPH_NUM "2")
set(CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM "3")
set(CONFIG_SOC_MPU_MIN_REGION_SIZE "0x20000000")
set(CONFIG_SOC_MPU_REGIONS_MAX_NUM "8")
set(CONFIG_SOC_PCNT_GROUPS "1")
set(CONFIG_SOC_PCNT_UNITS_PER_GROUP "8")
set(CONFIG_SOC_PCNT_CHANNELS_PER_UNIT "2")
set(CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT "2")
set(CONFIG_SOC_RMT_GROUPS "1")
set(CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP "8")
set(CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP "8")
set(CONFIG_SOC_RMT_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL "64")
set(CONFIG_SOC_RMT_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_RMT_SUPPORT_APB "y")
set(CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT "y")
set(CONFIG_SOC_RTCIO_PIN_COUNT "18")
set(CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_HOLD_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_WAKE_SUPPORTED "y")
set(CONFIG_SOC_SDM_GROUPS "1")
set(CONFIG_SOC_SDM_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_SDM_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED "y")
set(CONFIG_SOC_SPI_AS_CS_SUPPORTED "y")
set(CONFIG_SOC_SPI_PERIPH_NUM "3")
set(CONFIG_SOC_SPI_DMA_CHAN_NUM "2")
set(CONFIG_SOC_SPI_MAX_CS_NUM "3")
set(CONFIG_SOC_SPI_SUPPORT_CLK_APB "y")
set(CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPI_MAX_PRE_DIVIDER "8192")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED "y")
set(CONFIG_SOC_TIMER_GROUPS "2")
set(CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP "2")
set(CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH "64")
set(CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS "4")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_APB "y")
set(CONFIG_SOC_TOUCH_SENSOR_VERSION "1")
set(CONFIG_SOC_TOUCH_SENSOR_NUM "10")
set(CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM "1")
set(CONFIG_SOC_TWAI_CONTROLLER_NUM "1")
set(CONFIG_SOC_TWAI_BRP_MIN "2")
set(CONFIG_SOC_TWAI_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT "y")
set(CONFIG_SOC_UART_NUM "3")
set(CONFIG_SOC_UART_HP_NUM "3")
set(CONFIG_SOC_UART_SUPPORT_APB_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_UART_FIFO_LEN "128")
set(CONFIG_SOC_UART_BITRATE_MAX "5000000")
set(CONFIG_SOC_SPIRAM_SUPPORTED "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE "y")
set(CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG "y")
set(CONFIG_SOC_SHA_ENDIANNESS_BE "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA1 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA384 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512 "y")
set(CONFIG_SOC_MPI_MEM_BLOCKS_NUM "4")
set(CONFIG_SOC_MPI_OPERATIONS_NUM "y")
set(CONFIG_SOC_RSA_MAX_BIT_LEN "4096")
set(CONFIG_SOC_AES_SUPPORT_AES_128 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_192 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_256 "y")
set(CONFIG_SOC_SECURE_BOOT_V1 "y")
set(CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS "y")
set(CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX "32")
set(CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE "21")
set(CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RC_FAST_PD "y")
set(CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD "y")
set(CONFIG_SOC_PM_SUPPORT_MODEM_PD "y")
set(CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED "y")
set(CONFIG_SOC_CLK_APLL_SUPPORTED "y")
set(CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 "y")
set(CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION "y")
set(CONFIG_SOC_CLK_XTAL32K_SUPPORTED "y")
set(CONFIG_SOC_SDMMC_USE_IOMUX "y")
set(CONFIG_SOC_SDMMC_NUM_SLOTS "2")
set(CONFIG_SOC_WIFI_WAPI_SUPPORT "y")
set(CONFIG_SOC_WIFI_CSI_SUPPORT "y")
set(CONFIG_SOC_WIFI_MESH_SUPPORT "y")
set(CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW "y")
set(CONFIG_SOC_WIFI_NAN_SUPPORT "y")
set(CONFIG_SOC_BLE_SUPPORTED "y")
set(CONFIG_SOC_BLE_MESH_SUPPORTED "y")
set(CONFIG_SOC_BT_CLASSIC_SUPPORTED "y")
set(CONFIG_SOC_BLUFI_SUPPORTED "y")
set(CONFIG_SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED "y")
set(CONFIG_SOC_ULP_HAS_ADC "y")
set(CONFIG_SOC_PHY_COMBO_MODULE "y")
set(CONFIG_SOC_EMAC_RMII_CLK_OUT_INTERNAL_LOOPBACK "y")
set(CONFIG_IDF_CMAKE "y")
set(CONFIG_IDF_TOOLCHAIN "gcc")
set(CONFIG_IDF_TARGET_ARCH_XTENSA "y")
set(CONFIG_IDF_TARGET_ARCH "xtensa")
set(CONFIG_IDF_TARGET "esp32")
set(CONFIG_IDF_INIT_VERSION "5.3.2")
set(CONFIG_IDF_TARGET_ESP32 "y")
set(CONFIG_IDF_FIRMWARE_CHIP_ID "0x0")
set(CONFIG_APP_BUILD_TYPE_APP_2NDBOOT "y")
set(CONFIG_APP_BUILD_TYPE_RAM "")
set(CONFIG_APP_BUILD_GENERATE_BINARIES "y")
set(CONFIG_APP_BUILD_BOOTLOADER "y")
set(CONFIG_APP_BUILD_USE_FLASH_SECTIONS "y")
set(CONFIG_APP_REPRODUCIBLE_BUILD "")
set(CONFIG_APP_NO_BLOBS "")
set(CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS "")
set(CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS "")
set(CONFIG_BOOTLOADER_COMPILE_TIME_DATE "y")
set(CONFIG_BOOTLOADER_PROJECT_VER "1")
set(CONFIG_BOOTLOADER_OFFSET_IN_FLASH "0x1000")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE "y")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_ERROR "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_WARN "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_INFO "y")
set(CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL "3")
set(CONFIG_BOOTLOADER_FLASH_DC_AWARE "")
set(CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT "y")
set(CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_8V "")
set(CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V "y")
set(CONFIG_BOOTLOADER_FACTORY_RESET "")
set(CONFIG_BOOTLOADER_APP_TEST "")
set(CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE "")
set(CONFIG_BOOTLOADER_WDT_TIME_MS "9000")
set(CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS "")
set(CONFIG_BOOTLOADER_RESERVE_RTC_SIZE "0x0")
set(CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC "")
set(CONFIG_SECURE_BOOT_V1_SUPPORTED "y")
set(CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT "")
set(CONFIG_SECURE_BOOT "")
set(CONFIG_SECURE_FLASH_ENC_ENABLED "")
set(CONFIG_APP_COMPILE_TIME_DATE "y")
set(CONFIG_APP_EXCLUDE_PROJECT_VER_VAR "")
set(CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR "")
set(CONFIG_APP_PROJECT_VER_FROM_CONFIG "")
set(CONFIG_APP_RETRIEVE_LEN_ELF_SHA "9")
set(CONFIG_ESP_ROM_HAS_CRC_LE "y")
set(CONFIG_ESP_ROM_HAS_CRC_BE "y")
set(CONFIG_ESP_ROM_HAS_MZ_CRC32 "y")
set(CONFIG_ESP_ROM_HAS_JPEG_DECODE "y")
set(CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH "y")
set(CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_32BIT_TIME "y")
set(CONFIG_ESP_ROM_HAS_SW_FLOAT "y")
set(CONFIG_ESP_ROM_USB_OTG_NUM "-1")
set(CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM "-1")
set(CONFIG_ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB "y")
set(CONFIG_ESPTOOLPY_NO_STUB "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QIO "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QOUT "")
set(CONFIG_ESPTOOLPY_FLASHMODE_DIO "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_DOUT "")
set(CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR "y")
set(CONFIG_ESPTOOLPY_FLASHMODE "dio")
set(CONFIG_ESPTOOLPY_FLASHFREQ_80M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_40M "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ_26M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_20M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ "40m")
set(CONFIG_ESPTOOLPY_FLASHSIZE_1MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_2MB "y")
set(CONFIG_ESPTOOLPY_FLASHSIZE_4MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_8MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_16MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_32MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_64MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_128MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE "2MB")
set(CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE "")
set(CONFIG_ESPTOOLPY_BEFORE_RESET "y")
set(CONFIG_ESPTOOLPY_BEFORE_NORESET "")
set(CONFIG_ESPTOOLPY_BEFORE "default_reset")
set(CONFIG_ESPTOOLPY_AFTER_RESET "y")
set(CONFIG_ESPTOOLPY_AFTER_NORESET "")
set(CONFIG_ESPTOOLPY_AFTER "hard_reset")
set(CONFIG_ESPTOOLPY_MONITOR_BAUD "115200")
set(CONFIG_PARTITION_TABLE_SINGLE_APP "y")
set(CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE "")
set(CONFIG_PARTITION_TABLE_TWO_OTA "")
set(CONFIG_PARTITION_TABLE_CUSTOM "")
set(CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv")
set(CONFIG_PARTITION_TABLE_FILENAME "partitions_singleapp.csv")
set(CONFIG_PARTITION_TABLE_OFFSET "0x8000")
set(CONFIG_PARTITION_TABLE_MD5 "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_SIZE "")
set(CONFIG_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT "")
set(CONFIG_COMPILER_HIDE_PATHS_MACROS "y")
set(CONFIG_COMPILER_CXX_EXCEPTIONS "")
set(CONFIG_COMPILER_CXX_RTTI "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NONE "y")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NORM "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_STRONG "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_ALL "")
set(CONFIG_COMPILER_WARN_WRITE_STRINGS "")
set(CONFIG_COMPILER_DISABLE_GCC12_WARNINGS "")
set(CONFIG_COMPILER_DISABLE_GCC13_WARNINGS "")
set(CONFIG_COMPILER_DUMP_RTL_FILES "")
set(CONFIG_COMPILER_RT_LIB_GCCLIB "y")
set(CONFIG_COMPILER_RT_LIB_NAME "gcc")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING "")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE "y")
set(CONFIG_APPTRACE_DEST_JTAG "")
set(CONFIG_APPTRACE_DEST_NONE "y")
set(CONFIG_APPTRACE_DEST_UART1 "")
set(CONFIG_APPTRACE_DEST_UART2 "")
set(CONFIG_APPTRACE_DEST_UART_NONE "y")
set(CONFIG_APPTRACE_UART_TASK_PRIO "1")
set(CONFIG_APPTRACE_LOCK_ENABLE "y")
set(CONFIG_BT_ENABLED "")
set(CONFIG_BT_ALARM_MAX_NUM "50")
set(CONFIG_CONSOLE_SORTED_HELP "")
set(CONFIG_TWAI_ISR_IN_IRAM "")
set(CONFIG_TWAI_ERRATA_FIX_BUS_OFF_REC "y")
set(CONFIG_TWAI_ERRATA_FIX_TX_INTR_LOST "y")
set(CONFIG_TWAI_ERRATA_FIX_RX_FRAME_INVALID "y")
set(CONFIG_TWAI_ERRATA_FIX_RX_FIFO_CORRUPT "y")
set(CONFIG_TWAI_ERRATA_FIX_LISTEN_ONLY_DOM "y")
set(CONFIG_ADC_DISABLE_DAC "y")
set(CONFIG_ADC_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_ADC_CAL_EFUSE_TP_ENABLE "y")
set(CONFIG_ADC_CAL_EFUSE_VREF_ENABLE "y")
set(CONFIG_ADC_CAL_LUT_ENABLE "y")
set(CONFIG_ADC_CALI_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_DAC_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_MCPWM_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_RMT_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_I2S_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_PCNT_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_SDM_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_EFUSE_CUSTOM_TABLE "")
set(CONFIG_EFUSE_VIRTUAL "")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_NONE "")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4 "y")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_REPEAT "")
set(CONFIG_EFUSE_MAX_BLK_LEN "192")
set(CONFIG_ESP_TLS_USING_MBEDTLS "y")
set(CONFIG_ESP_TLS_USE_SECURE_ELEMENT "")
set(CONFIG_ESP_TLS_CLIENT_SESSION_TICKETS "")
set(CONFIG_ESP_TLS_SERVER_SESSION_TICKETS "")
set(CONFIG_ESP_TLS_SERVER_CERT_SELECT_HOOK "")
set(CONFIG_ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL "")
set(CONFIG_ESP_TLS_PSK_VERIFICATION "")
set(CONFIG_ESP_TLS_INSECURE "")
set(CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM "")
set(CONFIG_ADC_CONTINUOUS_ISR_IRAM_SAFE "")
set(CONFIG_ADC_CALI_EFUSE_TP_ENABLE "y")
set(CONFIG_ADC_CALI_EFUSE_VREF_ENABLE "y")
set(CONFIG_ADC_CALI_LUT_ENABLE "y")
set(CONFIG_ADC_DISABLE_DAC_OUTPUT "y")
set(CONFIG_ADC_ENABLE_DEBUG_LOG "")
set(CONFIG_ESP_COEX_ENABLED "y")
set(CONFIG_ESP_COEX_GPIO_DEBUG "")
set(CONFIG_ESP_ERR_TO_NAME_LOOKUP "y")
set(CONFIG_DAC_CTRL_FUNC_IN_IRAM "")
set(CONFIG_DAC_ISR_IRAM_SAFE "")
set(CONFIG_DAC_ENABLE_DEBUG_LOG "")
set(CONFIG_DAC_DMA_AUTO_16BIT_ALIGN "y")
set(CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL "")
set(CONFIG_GPIO_CTRL_FUNC_IN_IRAM "")
set(CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM "y")
set(CONFIG_GPTIMER_CTRL_FUNC_IN_IRAM "")
set(CONFIG_GPTIMER_ISR_IRAM_SAFE "")
set(CONFIG_GPTIMER_ENABLE_DEBUG_LOG "")
set(CONFIG_I2C_ISR_IRAM_SAFE "")
set(CONFIG_I2C_ENABLE_DEBUG_LOG "")
set(CONFIG_I2S_ISR_IRAM_SAFE "")
set(CONFIG_I2S_ENABLE_DEBUG_LOG "")
set(CONFIG_LEDC_CTRL_FUNC_IN_IRAM "")
set(CONFIG_MCPWM_ISR_IRAM_SAFE "")
set(CONFIG_MCPWM_CTRL_FUNC_IN_IRAM "")
set(CONFIG_MCPWM_ENABLE_DEBUG_LOG "")
set(CONFIG_PCNT_CTRL_FUNC_IN_IRAM "")
set(CONFIG_PCNT_ISR_IRAM_SAFE "")
set(CONFIG_PCNT_ENABLE_DEBUG_LOG "")
set(CONFIG_RMT_ISR_IRAM_SAFE "")
set(CONFIG_RMT_RECV_FUNC_IN_IRAM "")
set(CONFIG_RMT_ENABLE_DEBUG_LOG "")
set(CONFIG_SDM_CTRL_FUNC_IN_IRAM "")
set(CONFIG_SDM_ENABLE_DEBUG_LOG "")
set(CONFIG_SPI_MASTER_IN_IRAM "")
set(CONFIG_SPI_MASTER_ISR_IN_IRAM "y")
set(CONFIG_SPI_SLAVE_IN_IRAM "")
set(CONFIG_SPI_SLAVE_ISR_IN_IRAM "y")
set(CONFIG_TOUCH_CTRL_FUNC_IN_IRAM "")
set(CONFIG_TOUCH_ISR_IRAM_SAFE "")
set(CONFIG_TOUCH_ENABLE_DEBUG_LOG "")
set(CONFIG_UART_ISR_IN_IRAM "")
set(CONFIG_ETH_ENABLED "y")
set(CONFIG_ETH_USE_ESP32_EMAC "y")
set(CONFIG_ETH_PHY_INTERFACE_RMII "y")
set(CONFIG_ETH_RMII_CLK_INPUT "y")
set(CONFIG_ETH_RMII_CLK_OUTPUT "")
set(CONFIG_ETH_RMII_CLK_IN_GPIO "0")
set(CONFIG_ETH_DMA_BUFFER_SIZE "512")
set(CONFIG_ETH_DMA_RX_BUFFER_NUM "10")
set(CONFIG_ETH_DMA_TX_BUFFER_NUM "10")
set(CONFIG_ETH_IRAM_OPTIMIZATION "")
set(CONFIG_ETH_USE_SPI_ETHERNET "y")
set(CONFIG_ETH_SPI_ETHERNET_DM9051 "")
set(CONFIG_ETH_SPI_ETHERNET_W5500 "")
set(CONFIG_ETH_SPI_ETHERNET_KSZ8851SNL "")
set(CONFIG_ETH_USE_OPENETH "")
set(CONFIG_ETH_TRANSMIT_MUTEX "")
set(CONFIG_ESP_EVENT_LOOP_PROFILING "")
set(CONFIG_ESP_EVENT_POST_FROM_ISR "y")
set(CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR "y")
set(CONFIG_ESP_GDBSTUB_ENABLED "y")
set(CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME "")
set(CONFIG_ESP_GDBSTUB_SUPPORT_TASKS "y")
set(CONFIG_ESP_GDBSTUB_MAX_TASKS "32")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS "y")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH "")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH "")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT "")
set(CONFIG_HTTPD_MAX_REQ_HDR_LEN "512")
set(CONFIG_HTTPD_MAX_URI_LEN "512")
set(CONFIG_HTTPD_ERR_RESP_NO_DELAY "y")
set(CONFIG_HTTPD_PURGE_BUF_LEN "32")
set(CONFIG_HTTPD_LOG_PURGE_DATA "")
set(CONFIG_HTTPD_WS_SUPPORT "")
set(CONFIG_HTTPD_QUEUE_WORK_BLOCKING "")
set(CONFIG_ESP_HTTPS_OTA_DECRYPT_CB "")
set(CONFIG_ESP_HTTPS_OTA_ALLOW_HTTP "")
set(CONFIG_ESP_HTTPS_SERVER_ENABLE "")
set(CONFIG_ESP32_REV_MIN_0 "y")
set(CONFIG_ESP32_REV_MIN_1 "")
set(CONFIG_ESP32_REV_MIN_1_1 "")
set(CONFIG_ESP32_REV_MIN_2 "")
set(CONFIG_ESP32_REV_MIN_3 "")
set(CONFIG_ESP32_REV_MIN_3_1 "")
set(CONFIG_ESP32_REV_MIN "0")
set(CONFIG_ESP32_REV_MIN_FULL "0")
set(CONFIG_ESP_REV_MIN_FULL "0")
set(CONFIG_ESP32_REV_MAX_FULL "399")
set(CONFIG_ESP_REV_MAX_FULL "399")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL "0")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL "99")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_BT "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO "")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP_MAC_IGNORE_MAC_CRC_ERROR "")
set(CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC "")
set(CONFIG_ESP_SLEEP_POWER_DOWN_FLASH "")
set(CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU "")
set(CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND "")
set(CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY "2000")
set(CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION "")
set(CONFIG_ESP_SLEEP_DEBUG "")
set(CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS "y")
set(CONFIG_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_XTAL_FREQ_26 "")
set(CONFIG_XTAL_FREQ_40 "y")
set(CONFIG_XTAL_FREQ_AUTO "")
set(CONFIG_XTAL_FREQ "40")
set(CONFIG_ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM "y")
set(CONFIG_LCD_ENABLE_DEBUG_LOG "")
set(CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL "120")
set(CONFIG_ESP_NETIF_TCPIP_LWIP "y")
set(CONFIG_ESP_NETIF_LOOPBACK "")
set(CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API "y")
set(CONFIG_ESP_NETIF_RECEIVE_REPORT_ERRORS "")
set(CONFIG_ESP_NETIF_L2_TAP "")
set(CONFIG_ESP_NETIF_BRIDGE_EN "")
set(CONFIG_ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF "")
set(CONFIG_ESP_PHY_ENABLED "y")
set(CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE "y")
set(CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION "")
set(CONFIG_ESP_PHY_MAX_WIFI_TX_POWER "20")
set(CONFIG_ESP_PHY_MAX_TX_POWER "20")
set(CONFIG_ESP_PHY_REDUCE_TX_POWER "")
set(CONFIG_ESP_PHY_ENABLE_CERT_TEST "")
set(CONFIG_ESP_PHY_RF_CAL_PARTIAL "y")
set(CONFIG_ESP_PHY_RF_CAL_NONE "")
set(CONFIG_ESP_PHY_RF_CAL_FULL "")
set(CONFIG_ESP_PHY_CALIBRATION_MODE "0")
set(CONFIG_ESP_PHY_PLL_TRACK_DEBUG "")
set(CONFIG_PM_ENABLE "")
set(CONFIG_PM_SLP_IRAM_OPT "")
set(CONFIG_SPIRAM "")
set(CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ "160")
set(CONFIG_ESP32_USE_FIXED_STATIC_RAM_SIZE "")
set(CONFIG_ESP_SYSTEM_ESP32_SRAM1_REGION_AS_IRAM "")
set(CONFIG_ESP32_TRAX "")
set(CONFIG_ESP32_TRACEMEM_RESERVE_DRAM "0x0")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP_SYSTEM_PANIC_GDBSTUB "")
set(CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS "0")
set(CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1 "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY "0x0")
set(CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE "2048")
set(CONFIG_ESP_CONSOLE_UART_DEFAULT "y")
set(CONFIG_ESP_CONSOLE_UART_CUSTOM "")
set(CONFIG_ESP_CONSOLE_NONE "")
set(CONFIG_ESP_CONSOLE_UART "y")
set(CONFIG_ESP_CONSOLE_UART_NUM "0")
set(CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM "0")
set(CONFIG_ESP_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_ESP_INT_WDT "y")
set(CONFIG_ESP_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_ESP_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_ESP_TASK_WDT_EN "y")
set(CONFIG_ESP_TASK_WDT_INIT "y")
set(CONFIG_ESP_TASK_WDT_PANIC "")
set(CONFIG_ESP_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP_PANIC_HANDLER_IRAM "")
set(CONFIG_ESP_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP_DEBUG_OCDAWARE "y")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_5 "")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 "y")
set(CONFIG_ESP_BROWNOUT_DET "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL "0")
set(CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE "")
set(CONFIG_ESP_SYSTEM_BROWNOUT_INTR "y")
set(CONFIG_ESP_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_ESP_IPC_USES_CALLERS_PRIORITY "y")
set(CONFIG_ESP_IPC_ISR_ENABLE "y")
set(CONFIG_ESP_TIMER_PROFILING "")
set(CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER "y")
set(CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER "y")
set(CONFIG_ESP_TIMER_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_TIMER_INTERRUPT_LEVEL "1")
set(CONFIG_ESP_TIMER_SHOW_EXPERIMENTAL "")
set(CONFIG_ESP_TIMER_TASK_AFFINITY "0x0")
set(CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0 "y")
set(CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD "")
set(CONFIG_ESP_TIMER_IMPL_TG0_LAC "y")
set(CONFIG_ESP_WIFI_ENABLED "y")
set(CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM "10")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM "32")
set(CONFIG_ESP_WIFI_STATIC_TX_BUFFER "")
set(CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER "y")
set(CONFIG_ESP_WIFI_TX_BUFFER_TYPE "1")
set(CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM "32")
set(CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER "y")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER "")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF "0")
set(CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF "5")
set(CONFIG_ESP_WIFI_CSI_ENABLED "")
set(CONFIG_ESP_WIFI_AMPDU_TX_ENABLED "y")
set(CONFIG_ESP_WIFI_TX_BA_WIN "6")
set(CONFIG_ESP_WIFI_AMPDU_RX_ENABLED "y")
set(CONFIG_ESP_WIFI_RX_BA_WIN "6")
set(CONFIG_ESP_WIFI_NVS_ENABLED "y")
set(CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_0 "y")
set(CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_1 "")
set(CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN "752")
set(CONFIG_ESP_WIFI_MGMT_SBUF_NUM "32")
set(CONFIG_ESP_WIFI_IRAM_OPT "y")
set(CONFIG_ESP_WIFI_EXTRA_IRAM_OPT "")
set(CONFIG_ESP_WIFI_RX_IRAM_OPT "y")
set(CONFIG_ESP_WIFI_ENABLE_WPA3_SAE "y")
set(CONFIG_ESP_WIFI_ENABLE_SAE_PK "y")
set(CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT "y")
set(CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA "y")
set(CONFIG_ESP_WIFI_SLP_IRAM_OPT "")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME "50")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME "10")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME "15")
set(CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE "y")
set(CONFIG_ESP_WIFI_GMAC_SUPPORT "y")
set(CONFIG_ESP_WIFI_SOFTAP_SUPPORT "y")
set(CONFIG_ESP_WIFI_SLP_BEACON_LOST_OPT "")
set(CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM "7")
set(CONFIG_ESP_WIFI_NAN_ENABLE "")
set(CONFIG_ESP_WIFI_MBEDTLS_CRYPTO "y")
set(CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_ESP_WIFI_WAPI_PSK "")
set(CONFIG_ESP_WIFI_11KV_SUPPORT "")
set(CONFIG_ESP_WIFI_MBO_SUPPORT "")
set(CONFIG_ESP_WIFI_DPP_SUPPORT "")
set(CONFIG_ESP_WIFI_11R_SUPPORT "")
set(CONFIG_ESP_WIFI_WPS_SOFTAP_REGISTRAR "")
set(CONFIG_ESP_WIFI_WPS_STRICT "")
set(CONFIG_ESP_WIFI_WPS_PASSPHRASE "")
set(CONFIG_ESP_WIFI_DEBUG_PRINT "")
set(CONFIG_ESP_WIFI_TESTING_OPTIONS "")
set(CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT "y")
set(CONFIG_ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER "")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH "")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_UART "")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_NONE "y")
set(CONFIG_FATFS_VOLUME_COUNT "2")
set(CONFIG_FATFS_LFN_NONE "y")
set(CONFIG_FATFS_LFN_HEAP "")
set(CONFIG_FATFS_LFN_STACK "")
set(CONFIG_FATFS_SECTOR_512 "")
set(CONFIG_FATFS_SECTOR_4096 "y")
set(CONFIG_FATFS_CODEPAGE_DYNAMIC "")
set(CONFIG_FATFS_CODEPAGE_437 "y")
set(CONFIG_FATFS_CODEPAGE_720 "")
set(CONFIG_FATFS_CODEPAGE_737 "")
set(CONFIG_FATFS_CODEPAGE_771 "")
set(CONFIG_FATFS_CODEPAGE_775 "")
set(CONFIG_FATFS_CODEPAGE_850 "")
set(CONFIG_FATFS_CODEPAGE_852 "")
set(CONFIG_FATFS_CODEPAGE_855 "")
set(CONFIG_FATFS_CODEPAGE_857 "")
set(CONFIG_FATFS_CODEPAGE_860 "")
set(CONFIG_FATFS_CODEPAGE_861 "")
set(CONFIG_FATFS_CODEPAGE_862 "")
set(CONFIG_FATFS_CODEPAGE_863 "")
set(CONFIG_FATFS_CODEPAGE_864 "")
set(CONFIG_FATFS_CODEPAGE_865 "")
set(CONFIG_FATFS_CODEPAGE_866 "")
set(CONFIG_FATFS_CODEPAGE_869 "")
set(CONFIG_FATFS_CODEPAGE_932 "")
set(CONFIG_FATFS_CODEPAGE_936 "")
set(CONFIG_FATFS_CODEPAGE_949 "")
set(CONFIG_FATFS_CODEPAGE_950 "")
set(CONFIG_FATFS_CODEPAGE "437")
set(CONFIG_FATFS_FS_LOCK "0")
set(CONFIG_FATFS_TIMEOUT_MS "10000")
set(CONFIG_FATFS_PER_FILE_CACHE "y")
set(CONFIG_FATFS_USE_FASTSEEK "")
set(CONFIG_FATFS_VFS_FSTAT_BLKSIZE "0")
set(CONFIG_FATFS_IMMEDIATE_FSYNC "")
set(CONFIG_FATFS_USE_LABEL "")
set(CONFIG_FATFS_LINK_LOCK "y")
set(CONFIG_FREERTOS_SMP "")
set(CONFIG_FREERTOS_UNICORE "")
set(CONFIG_FREERTOS_HZ "100")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY "y")
set(CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS "1")
set(CONFIG_FREERTOS_IDLE_TASK_STACKSIZE "1536")
set(CONFIG_FREERTOS_USE_IDLE_HOOK "")
set(CONFIG_FREERTOS_USE_TICK_HOOK "")
set(CONFIG_FREERTOS_MAX_TASK_NAME_LEN "16")
set(CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY "")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0 "")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1 "")
set(CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY "y")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_TIMER_TASK_PRIORITY "1")
set(CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_FREERTOS_TIMER_QUEUE_LENGTH "10")
set(CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE "0")
set(CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES "1")
set(CONFIG_FREERTOS_USE_TRACE_FACILITY "")
set(CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES "")
set(CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS "")
set(CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG "")
set(CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER "y")
set(CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK "")
set(CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS "y")
set(CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK "")
set(CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP "")
set(CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER "y")
set(CONFIG_FREERTOS_ISR_STACKSIZE "1536")
set(CONFIG_FREERTOS_INTERRUPT_BACKTRACE "y")
set(CONFIG_FREERTOS_FPU_IN_ISR "")
set(CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER "y")
set(CONFIG_FREERTOS_CORETIMER_0 "y")
set(CONFIG_FREERTOS_CORETIMER_1 "")
set(CONFIG_FREERTOS_SYSTICK_USES_CCOUNT "y")
set(CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE "")
set(CONFIG_FREERTOS_PORT "y")
set(CONFIG_FREERTOS_NO_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION "y")
set(CONFIG_FREERTOS_DEBUG_OCDAWARE "y")
set(CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT "y")
set(CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH "y")
set(CONFIG_FREERTOS_NUMBER_OF_CORES "2")
set(CONFIG_HAL_ASSERTION_EQUALS_SYSTEM "y")
set(CONFIG_HAL_ASSERTION_DISABLE "")
set(CONFIG_HAL_ASSERTION_SILENT "")
set(CONFIG_HAL_ASSERTION_ENABLE "")
set(CONFIG_HAL_DEFAULT_ASSERTION_LEVEL "2")
set(CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM "y")
set(CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM "y")
set(CONFIG_HAL_ECDSA_GEN_SIG_CM "")
set(CONFIG_HEAP_POISONING_DISABLED "y")
set(CONFIG_HEAP_POISONING_LIGHT "")
set(CONFIG_HEAP_POISONING_COMPREHENSIVE "")
set(CONFIG_HEAP_TRACING_OFF "y")
set(CONFIG_HEAP_TRACING_STANDALONE "")
set(CONFIG_HEAP_TRACING_TOHOST "")
set(CONFIG_HEAP_USE_HOOKS "")
set(CONFIG_HEAP_TASK_TRACKING "")
set(CONFIG_HEAP_ABORT_WHEN_ALLOCATION_FAILS "")
set(CONFIG_HEAP_PLACE_FUNCTION_INTO_FLASH "")
set(CONFIG_LOG_DEFAULT_LEVEL_NONE "")
set(CONFIG_LOG_DEFAULT_LEVEL_ERROR "")
set(CONFIG_LOG_DEFAULT_LEVEL_WARN "")
set(CONFIG_LOG_DEFAULT_LEVEL_INFO "y")
set(CONFIG_LOG_DEFAULT_LEVEL_DEBUG "")
set(CONFIG_LOG_DEFAULT_LEVEL_VERBOSE "")
set(CONFIG_LOG_DEFAULT_LEVEL "3")
set(CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT "y")
set(CONFIG_LOG_MAXIMUM_LEVEL_DEBUG "")
set(CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE "")
set(CONFIG_LOG_MAXIMUM_LEVEL "3")
set(CONFIG_LOG_MASTER_LEVEL "")
set(CONFIG_LOG_COLORS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_RTOS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM "")
set(CONFIG_LWIP_ENABLE "y")
set(CONFIG_LWIP_LOCAL_HOSTNAME "espressif")
set(CONFIG_LWIP_NETIF_API "")
set(CONFIG_LWIP_TCPIP_TASK_PRIO "18")
set(CONFIG_LWIP_TCPIP_CORE_LOCKING "")
set(CONFIG_LWIP_CHECK_THREAD_SAFETY "")
set(CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES "y")
set(CONFIG_LWIP_L2_TO_L3_COPY "")
set(CONFIG_LWIP_IRAM_OPTIMIZATION "")
set(CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION "")
set(CONFIG_LWIP_TIMERS_ONDEMAND "y")
set(CONFIG_LWIP_ND6 "y")
set(CONFIG_LWIP_FORCE_ROUTER_FORWARDING "")
set(CONFIG_LWIP_MAX_SOCKETS "10")
set(CONFIG_LWIP_USE_ONLY_LWIP_SELECT "")
set(CONFIG_LWIP_SO_LINGER "")
set(CONFIG_LWIP_SO_REUSE "y")
set(CONFIG_LWIP_SO_REUSE_RXTOALL "y")
set(CONFIG_LWIP_SO_RCVBUF "")
set(CONFIG_LWIP_NETBUF_RECVINFO "")
set(CONFIG_LWIP_IP_DEFAULT_TTL "64")
set(CONFIG_LWIP_IP4_FRAG "y")
set(CONFIG_LWIP_IP6_FRAG "y")
set(CONFIG_LWIP_IP4_REASSEMBLY "")
set(CONFIG_LWIP_IP6_REASSEMBLY "")
set(CONFIG_LWIP_IP_REASS_MAX_PBUFS "10")
set(CONFIG_LWIP_IP_FORWARD "")
set(CONFIG_LWIP_STATS "")
set(CONFIG_LWIP_ESP_GRATUITOUS_ARP "y")
set(CONFIG_LWIP_GARP_TMR_INTERVAL "60")
set(CONFIG_LWIP_ESP_MLDV6_REPORT "y")
set(CONFIG_LWIP_MLDV6_TMR_INTERVAL "40")
set(CONFIG_LWIP_TCPIP_RECVMBOX_SIZE "32")
set(CONFIG_LWIP_DHCP_DOES_ARP_CHECK "y")
set(CONFIG_LWIP_DHCP_DISABLE_CLIENT_ID "")
set(CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID "y")
set(CONFIG_LWIP_DHCP_RESTORE_LAST_IP "")
set(CONFIG_LWIP_DHCP_OPTIONS_LEN "68")
set(CONFIG_LWIP_NUM_NETIF_CLIENT_DATA "0")
set(CONFIG_LWIP_DHCP_COARSE_TIMER_SECS "1")
set(CONFIG_LWIP_DHCPS "y")
set(CONFIG_LWIP_DHCPS_LEASE_UNIT "60")
set(CONFIG_LWIP_DHCPS_MAX_STATION_NUM "8")
set(CONFIG_LWIP_DHCPS_STATIC_ENTRIES "y")
set(CONFIG_LWIP_AUTOIP "")
set(CONFIG_LWIP_IPV4 "y")
set(CONFIG_LWIP_IPV6 "y")
set(CONFIG_LWIP_IPV6_AUTOCONFIG "")
set(CONFIG_LWIP_IPV6_NUM_ADDRESSES "3")
set(CONFIG_LWIP_IPV6_FORWARD "")
set(CONFIG_LWIP_NETIF_STATUS_CALLBACK "")
set(CONFIG_LWIP_NETIF_LOOPBACK "y")
set(CONFIG_LWIP_LOOPBACK_MAX_PBUFS "8")
set(CONFIG_LWIP_MAX_ACTIVE_TCP "16")
set(CONFIG_LWIP_MAX_LISTENING_TCP "16")
set(CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION "y")
set(CONFIG_LWIP_TCP_MAXRTX "12")
set(CONFIG_LWIP_TCP_SYNMAXRTX "12")
set(CONFIG_LWIP_TCP_MSS "1440")
set(CONFIG_LWIP_TCP_TMR_INTERVAL "250")
set(CONFIG_LWIP_TCP_MSL "60000")
set(CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT "20000")
set(CONFIG_LWIP_TCP_SND_BUF_DEFAULT "5760")
set(CONFIG_LWIP_TCP_WND_DEFAULT "5760")
set(CONFIG_LWIP_TCP_RECVMBOX_SIZE "6")
set(CONFIG_LWIP_TCP_ACCEPTMBOX_SIZE "6")
set(CONFIG_LWIP_TCP_QUEUE_OOSEQ "y")
set(CONFIG_LWIP_TCP_OOSEQ_TIMEOUT "6")
set(CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS "4")
set(CONFIG_LWIP_TCP_SACK_OUT "")
set(CONFIG_LWIP_TCP_OVERSIZE_MSS "y")
set(CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS "")
set(CONFIG_LWIP_TCP_OVERSIZE_DISABLE "")
set(CONFIG_LWIP_TCP_RTO_TIME "1500")
set(CONFIG_LWIP_MAX_UDP_PCBS "16")
set(CONFIG_LWIP_UDP_RECVMBOX_SIZE "6")
set(CONFIG_LWIP_CHECKSUM_CHECK_IP "")
set(CONFIG_LWIP_CHECKSUM_CHECK_UDP "")
set(CONFIG_LWIP_CHECKSUM_CHECK_ICMP "y")
set(CONFIG_LWIP_TCPIP_TASK_STACK_SIZE "3072")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY "y")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU0 "")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU1 "")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY "0x7fffffff")
set(CONFIG_LWIP_IPV6_ND6_NUM_PREFIXES "5")
set(CONFIG_LWIP_IPV6_ND6_NUM_ROUTERS "3")
set(CONFIG_LWIP_IPV6_ND6_NUM_DESTINATIONS "10")
set(CONFIG_LWIP_PPP_SUPPORT "")
set(CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE "3")
set(CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS "5")
set(CONFIG_LWIP_SLIP_SUPPORT "")
set(CONFIG_LWIP_ICMP "y")
set(CONFIG_LWIP_MULTICAST_PING "")
set(CONFIG_LWIP_BROADCAST_PING "")
set(CONFIG_LWIP_MAX_RAW_PCBS "16")
set(CONFIG_LWIP_SNTP_MAX_SERVERS "1")
set(CONFIG_LWIP_DHCP_GET_NTP_SRV "")
set(CONFIG_LWIP_SNTP_UPDATE_DELAY "3600000")
set(CONFIG_LWIP_SNTP_STARTUP_DELAY "y")
set(CONFIG_LWIP_SNTP_MAXIMUM_STARTUP_DELAY "5000")
set(CONFIG_LWIP_DNS_MAX_HOST_IP "1")
set(CONFIG_LWIP_DNS_MAX_SERVERS "3")
set(CONFIG_LWIP_FALLBACK_DNS_SERVER_SUPPORT "")
set(CONFIG_LWIP_DNS_SETSERVER_WITH_NETIF "")
set(CONFIG_LWIP_BRIDGEIF_MAX_PORTS "7")
set(CONFIG_LWIP_ESP_LWIP_ASSERT "y")
set(CONFIG_LWIP_HOOK_TCP_ISN_NONE "")
set(CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT "y")
set(CONFIG_LWIP_HOOK_TCP_ISN_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_NONE "y")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT "")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_CUSTOM "")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_NONE "y")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT "")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE "y")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT "")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM "")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE "y")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT "")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM "")
set(CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_NONE "y")
set(CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_INPUT_NONE "")
set(CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT "y")
set(CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM "")
set(CONFIG_LWIP_DEBUG "")
set(CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC "y")
set(CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC "")
set(CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC "")
set(CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN "y")
set(CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN "16384")
set(CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN "4096")
set(CONFIG_MBEDTLS_DYNAMIC_BUFFER "")
set(CONFIG_MBEDTLS_DEBUG "")
set(CONFIG_MBEDTLS_SSL_PROTO_TLS1_3 "")
set(CONFIG_MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH "")
set(CONFIG_MBEDTLS_X509_TRUSTED_CERT_CALLBACK "")
set(CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION "")
set(CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE "y")
set(CONFIG_MBEDTLS_PKCS7_C "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE "")
set(CONFIG_MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS "200")
set(CONFIG_MBEDTLS_ECP_RESTARTABLE "")
set(CONFIG_MBEDTLS_CMAC_C "y")
set(CONFIG_MBEDTLS_HARDWARE_AES "y")
set(CONFIG_MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER "y")
set(CONFIG_MBEDTLS_HARDWARE_MPI "y")
set(CONFIG_MBEDTLS_LARGE_KEY_SOFTWARE_MPI "")
set(CONFIG_MBEDTLS_HARDWARE_SHA "y")
set(CONFIG_MBEDTLS_ROM_MD5 "y")
set(CONFIG_MBEDTLS_ATCA_HW_ECDSA_SIGN "")
set(CONFIG_MBEDTLS_ATCA_HW_ECDSA_VERIFY "")
set(CONFIG_MBEDTLS_HAVE_TIME "y")
set(CONFIG_MBEDTLS_PLATFORM_TIME_ALT "")
set(CONFIG_MBEDTLS_HAVE_TIME_DATE "")
set(CONFIG_MBEDTLS_ECDSA_DETERMINISTIC "y")
set(CONFIG_MBEDTLS_SHA512_C "y")
set(CONFIG_MBEDTLS_SHA3_C "")
set(CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT "y")
set(CONFIG_MBEDTLS_TLS_SERVER_ONLY "")
set(CONFIG_MBEDTLS_TLS_CLIENT_ONLY "")
set(CONFIG_MBEDTLS_TLS_DISABLED "")
set(CONFIG_MBEDTLS_TLS_SERVER "y")
set(CONFIG_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_MBEDTLS_TLS_ENABLED "y")
set(CONFIG_MBEDTLS_PSK_MODES "")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_RSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA "y")
set(CONFIG_MBEDTLS_SSL_RENEGOTIATION "y")
set(CONFIG_MBEDTLS_SSL_PROTO_TLS1_2 "y")
set(CONFIG_MBEDTLS_SSL_PROTO_GMTSSL1_1 "")
set(CONFIG_MBEDTLS_SSL_PROTO_DTLS "")
set(CONFIG_MBEDTLS_SSL_ALPN "y")
set(CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS "y")
set(CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS "y")
set(CONFIG_MBEDTLS_AES_C "y")
set(CONFIG_MBEDTLS_CAMELLIA_C "")
set(CONFIG_MBEDTLS_DES_C "")
set(CONFIG_MBEDTLS_BLOWFISH_C "")
set(CONFIG_MBEDTLS_XTEA_C "")
set(CONFIG_MBEDTLS_CCM_C "y")
set(CONFIG_MBEDTLS_GCM_C "y")
set(CONFIG_MBEDTLS_NIST_KW_C "")
set(CONFIG_MBEDTLS_RIPEMD160_C "")
set(CONFIG_MBEDTLS_PEM_PARSE_C "y")
set(CONFIG_MBEDTLS_PEM_WRITE_C "y")
set(CONFIG_MBEDTLS_X509_CRL_PARSE_C "y")
set(CONFIG_MBEDTLS_X509_CSR_PARSE_C "y")
set(CONFIG_MBEDTLS_ECP_C "y")
set(CONFIG_MBEDTLS_DHM_C "")
set(CONFIG_MBEDTLS_ECDH_C "y")
set(CONFIG_MBEDTLS_ECDSA_C "y")
set(CONFIG_MBEDTLS_ECJPAKE_C "")
set(CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_NIST_OPTIM "y")
set(CONFIG_MBEDTLS_ECP_FIXED_POINT_OPTIM "")
set(CONFIG_MBEDTLS_POLY1305_C "")
set(CONFIG_MBEDTLS_CHACHA20_C "")
set(CONFIG_MBEDTLS_HKDF_C "")
set(CONFIG_MBEDTLS_THREADING_C "")
set(CONFIG_MBEDTLS_ERROR_STRINGS "y")
set(CONFIG_MBEDTLS_FS_IO "y")
set(CONFIG_MQTT_PROTOCOL_311 "y")
set(CONFIG_MQTT_PROTOCOL_5 "")
set(CONFIG_MQTT_TRANSPORT_SSL "y")
set(CONFIG_MQTT_TRANSPORT_WEBSOCKET "y")
set(CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE "y")
set(CONFIG_MQTT_MSG_ID_INCREMENTAL "")
set(CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED "")
set(CONFIG_MQTT_REPORT_DELETED_MESSAGES "")
set(CONFIG_MQTT_USE_CUSTOM_CONFIG "")
set(CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED "")
set(CONFIG_MQTT_CUSTOM_OUTBOX "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF "y")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CR "y")
set(CONFIG_NEWLIB_NANO_FORMAT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE "")
set(CONFIG_NVS_ASSERT_ERROR_CHECK "")
set(CONFIG_NVS_LEGACY_DUP_KEYS_COMPATIBILITY "")
set(CONFIG_OPENTHREAD_ENABLED "")
set(CONFIG_OPENTHREAD_NETWORK_NAME "OpenThread-ESP")
set(CONFIG_OPENTHREAD_MESH_LOCAL_PREFIX "fd00:db8:a0:0::/64")
set(CONFIG_OPENTHREAD_NETWORK_CHANNEL "15")
set(CONFIG_OPENTHREAD_NETWORK_PANID "0x1234")
set(CONFIG_OPENTHREAD_NETWORK_EXTPANID "dead00beef00cafe")
set(CONFIG_OPENTHREAD_NETWORK_MASTERKEY "00112233445566778899aabbccddeeff")
set(CONFIG_OPENTHREAD_NETWORK_PSKC "104810e2315100afd6bc9215a6bfac53")
set(CONFIG_OPENTHREAD_XTAL_ACCURACY "130")
set(CONFIG_OPENTHREAD_SPINEL_ONLY "")
set(CONFIG_OPENTHREAD_RX_ON_WHEN_IDLE "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0 "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1 "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2 "y")
set(CONFIG_PTHREAD_TASK_PRIO_DEFAULT "5")
set(CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT "3072")
set(CONFIG_PTHREAD_STACK_MIN "768")
set(CONFIG_PTHREAD_DEFAULT_CORE_NO_AFFINITY "y")
set(CONFIG_PTHREAD_DEFAULT_CORE_0 "")
set(CONFIG_PTHREAD_DEFAULT_CORE_1 "")
set(CONFIG_PTHREAD_TASK_CORE_DEFAULT "-1")
set(CONFIG_PTHREAD_TASK_NAME_DEFAULT "pthread")
set(CONFIG_MMU_PAGE_SIZE_64KB "y")
set(CONFIG_MMU_PAGE_MODE "64KB")
set(CONFIG_MMU_PAGE_SIZE "0x10000")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC "y")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET "y")
set(CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US "50")
set(CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND "")
set(CONFIG_SPI_FLASH_VERIFY_WRITE "")
set(CONFIG_SPI_FLASH_ENABLE_COUNTERS "")
set(CONFIG_SPI_FLASH_ROM_DRIVER_PATCH "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED "")
set(CONFIG_SPI_FLASH_SHARE_SPI1_BUS "")
set(CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE "")
set(CONFIG_SPI_FLASH_YIELD_DURING_ERASE "y")
set(CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS "20")
set(CONFIG_SPI_FLASH_ERASE_YIELD_TICKS "1")
set(CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE "8192")
set(CONFIG_SPI_FLASH_SIZE_OVERRIDE "")
set(CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED "")
set(CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST "")
set(CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED "y")
set(CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_GD_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_TH_CHIP "")
set(CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE "y")
set(CONFIG_SPIFFS_MAX_PARTITIONS "3")
set(CONFIG_SPIFFS_CACHE "y")
set(CONFIG_SPIFFS_CACHE_WR "y")
set(CONFIG_SPIFFS_CACHE_STATS "")
set(CONFIG_SPIFFS_PAGE_CHECK "y")
set(CONFIG_SPIFFS_GC_MAX_RUNS "10")
set(CONFIG_SPIFFS_GC_STATS "")
set(CONFIG_SPIFFS_PAGE_SIZE "256")
set(CONFIG_SPIFFS_OBJ_NAME_LEN "32")
set(CONFIG_SPIFFS_FOLLOW_SYMLINKS "")
set(CONFIG_SPIFFS_USE_MAGIC "y")
set(CONFIG_SPIFFS_USE_MAGIC_LENGTH "y")
set(CONFIG_SPIFFS_META_LENGTH "4")
set(CONFIG_SPIFFS_USE_MTIME "y")
set(CONFIG_SPIFFS_DBG "")
set(CONFIG_SPIFFS_API_DBG "")
set(CONFIG_SPIFFS_GC_DBG "")
set(CONFIG_SPIFFS_CACHE_DBG "")
set(CONFIG_SPIFFS_CHECK_DBG "")
set(CONFIG_SPIFFS_TEST_VISUALISATION "")
set(CONFIG_WS_TRANSPORT "y")
set(CONFIG_WS_BUFFER_SIZE "1024")
set(CONFIG_WS_DYNAMIC_BUFFER "")
set(CONFIG_ULP_COPROC_ENABLED "")
set(CONFIG_UNITY_ENABLE_FLOAT "y")
set(CONFIG_UNITY_ENABLE_DOUBLE "y")
set(CONFIG_UNITY_ENABLE_64BIT "")
set(CONFIG_UNITY_ENABLE_COLOR "")
set(CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER "y")
set(CONFIG_UNITY_ENABLE_FIXTURE "")
set(CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL "")
set(CONFIG_VFS_SUPPORT_IO "y")
set(CONFIG_VFS_SUPPORT_DIR "y")
set(CONFIG_VFS_SUPPORT_SELECT "y")
set(CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT "y")
set(CONFIG_VFS_SELECT_IN_RAM "")
set(CONFIG_VFS_SUPPORT_TERMIOS "y")
set(CONFIG_VFS_MAX_COUNT "8")
set(CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS "1")
set(CONFIG_WL_SECTOR_SIZE_512 "")
set(CONFIG_WL_SECTOR_SIZE_4096 "y")
set(CONFIG_WL_SECTOR_SIZE "4096")
set(CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES "16")
set(CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT "30")
set(CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN "y")
set(CONFIG_WIFI_PROV_STA_FAST_SCAN "")
set(CONFIG_IDF_EXPERIMENTAL_FEATURES "")
set(CONFIGS_LIST CONFIG_SOC_BROWNOUT_RESET_SUPPORTED;CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED;CONFIG_SOC_DPORT_WORKAROUND;CONFIG_SOC_CAPS_ECO_VER_MAX;CONFIG_SOC_ADC_SUPPORTED;CONFIG_SOC_DAC_SUPPORTED;CONFIG_SOC_UART_SUPPORTED;CONFIG_SOC_MCPWM_SUPPORTED;CONFIG_SOC_GPTIMER_SUPPORTED;CONFIG_SOC_SDMMC_HOST_SUPPORTED;CONFIG_SOC_BT_SUPPORTED;CONFIG_SOC_PCNT_SUPPORTED;CONFIG_SOC_PHY_SUPPORTED;CONFIG_SOC_WIFI_SUPPORTED;CONFIG_SOC_SDIO_SLAVE_SUPPORTED;CONFIG_SOC_TWAI_SUPPORTED;CONFIG_SOC_EFUSE_SUPPORTED;CONFIG_SOC_EMAC_SUPPORTED;CONFIG_SOC_ULP_SUPPORTED;CONFIG_SOC_CCOMP_TIMER_SUPPORTED;CONFIG_SOC_RTC_FAST_MEM_SUPPORTED;CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED;CONFIG_SOC_RTC_MEM_SUPPORTED;CONFIG_SOC_I2S_SUPPORTED;CONFIG_SOC_RMT_SUPPORTED;CONFIG_SOC_SDM_SUPPORTED;CONFIG_SOC_GPSPI_SUPPORTED;CONFIG_SOC_LEDC_SUPPORTED;CONFIG_SOC_I2C_SUPPORTED;CONFIG_SOC_SUPPORT_COEXISTENCE;CONFIG_SOC_AES_SUPPORTED;CONFIG_SOC_MPI_SUPPORTED;CONFIG_SOC_SHA_SUPPORTED;CONFIG_SOC_FLASH_ENC_SUPPORTED;CONFIG_SOC_SECURE_BOOT_SUPPORTED;CONFIG_SOC_TOUCH_SENSOR_SUPPORTED;CONFIG_SOC_BOD_SUPPORTED;CONFIG_SOC_ULP_FSM_SUPPORTED;CONFIG_SOC_CLK_TREE_SUPPORTED;CONFIG_SOC_MPU_SUPPORTED;CONFIG_SOC_WDT_SUPPORTED;CONFIG_SOC_SPI_FLASH_SUPPORTED;CONFIG_SOC_RNG_SUPPORTED;CONFIG_SOC_LIGHT_SLEEP_SUPPORTED;CONFIG_SOC_DEEP_SLEEP_SUPPORTED;CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT;CONFIG_SOC_PM_SUPPORTED;CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL;CONFIG_SOC_XTAL_SUPPORT_26M;CONFIG_SOC_XTAL_SUPPORT_40M;CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT;CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED;CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED;CONFIG_SOC_ADC_DMA_SUPPORTED;CONFIG_SOC_ADC_PERIPH_NUM;CONFIG_SOC_ADC_MAX_CHANNEL_NUM;CONFIG_SOC_ADC_ATTEN_NUM;CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM;CONFIG_SOC_ADC_PATT_LEN_MAX;CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH;CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH;CONFIG_SOC_ADC_DIGI_RESULT_BYTES;CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV;CONFIG_SOC_ADC_DIGI_MONITOR_NUM;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW;CONFIG_SOC_ADC_RTC_MIN_BITWIDTH;CONFIG_SOC_ADC_RTC_MAX_BITWIDTH;CONFIG_SOC_ADC_SHARED_POWER;CONFIG_SOC_SHARED_IDCACHE_SUPPORTED;CONFIG_SOC_IDCACHE_PER_CORE;CONFIG_SOC_CPU_CORES_NUM;CONFIG_SOC_CPU_INTR_NUM;CONFIG_SOC_CPU_HAS_FPU;CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES;CONFIG_SOC_CPU_BREAKPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE;CONFIG_SOC_DAC_CHAN_NUM;CONFIG_SOC_DAC_RESOLUTION;CONFIG_SOC_DAC_DMA_16BIT_ALIGN;CONFIG_SOC_GPIO_PORT;CONFIG_SOC_GPIO_PIN_COUNT;CONFIG_SOC_GPIO_VALID_GPIO_MASK;CONFIG_SOC_GPIO_IN_RANGE_MAX;CONFIG_SOC_GPIO_OUT_RANGE_MAX;CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK;CONFIG_SOC_GPIO_CLOCKOUT_BY_IO_MUX;CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM;CONFIG_SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP;CONFIG_SOC_I2C_NUM;CONFIG_SOC_HP_I2C_NUM;CONFIG_SOC_I2C_FIFO_LEN;CONFIG_SOC_I2C_CMD_REG_NUM;CONFIG_SOC_I2C_SUPPORT_SLAVE;CONFIG_SOC_I2C_SUPPORT_APB;CONFIG_SOC_I2C_STOP_INDEPENDENT;CONFIG_SOC_I2S_NUM;CONFIG_SOC_I2S_HW_VERSION_1;CONFIG_SOC_I2S_SUPPORTS_APLL;CONFIG_SOC_I2S_SUPPORTS_PLL_F160M;CONFIG_SOC_I2S_SUPPORTS_PDM;CONFIG_SOC_I2S_SUPPORTS_PDM_TX;CONFIG_SOC_I2S_PDM_MAX_TX_LINES;CONFIG_SOC_I2S_SUPPORTS_PDM_RX;CONFIG_SOC_I2S_PDM_MAX_RX_LINES;CONFIG_SOC_I2S_SUPPORTS_ADC_DAC;CONFIG_SOC_I2S_SUPPORTS_ADC;CONFIG_SOC_I2S_SUPPORTS_DAC;CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA;CONFIG_SOC_I2S_MAX_DATA_WIDTH;CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD;CONFIG_SOC_I2S_LCD_I80_VARIANT;CONFIG_SOC_LCD_I80_SUPPORTED;CONFIG_SOC_LCD_I80_BUSES;CONFIG_SOC_LCD_I80_BUS_WIDTH;CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX;CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK;CONFIG_SOC_LEDC_SUPPORT_REF_TICK;CONFIG_SOC_LEDC_SUPPORT_HS_MODE;CONFIG_SOC_LEDC_CHANNEL_NUM;CONFIG_SOC_LEDC_TIMER_BIT_WIDTH;CONFIG_SOC_MCPWM_GROUPS;CONFIG_SOC_MCPWM_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP;CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR;CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER;CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP;CONFIG_SOC_MMU_PERIPH_NUM;CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM;CONFIG_SOC_MPU_MIN_REGION_SIZE;CONFIG_SOC_MPU_REGIONS_MAX_NUM;CONFIG_SOC_PCNT_GROUPS;CONFIG_SOC_PCNT_UNITS_PER_GROUP;CONFIG_SOC_PCNT_CHANNELS_PER_UNIT;CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT;CONFIG_SOC_RMT_GROUPS;CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_CHANNELS_PER_GROUP;CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL;CONFIG_SOC_RMT_SUPPORT_REF_TICK;CONFIG_SOC_RMT_SUPPORT_APB;CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT;CONFIG_SOC_RTCIO_PIN_COUNT;CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED;CONFIG_SOC_RTCIO_HOLD_SUPPORTED;CONFIG_SOC_RTCIO_WAKE_SUPPORTED;CONFIG_SOC_SDM_GROUPS;CONFIG_SOC_SDM_CHANNELS_PER_GROUP;CONFIG_SOC_SDM_CLK_SUPPORT_APB;CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED;CONFIG_SOC_SPI_AS_CS_SUPPORTED;CONFIG_SOC_SPI_PERIPH_NUM;CONFIG_SOC_SPI_DMA_CHAN_NUM;CONFIG_SOC_SPI_MAX_CS_NUM;CONFIG_SOC_SPI_SUPPORT_CLK_APB;CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPI_MAX_PRE_DIVIDER;CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED;CONFIG_SOC_TIMER_GROUPS;CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP;CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH;CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS;CONFIG_SOC_TIMER_GROUP_SUPPORT_APB;CONFIG_SOC_TOUCH_SENSOR_VERSION;CONFIG_SOC_TOUCH_SENSOR_NUM;CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM;CONFIG_SOC_TWAI_CONTROLLER_NUM;CONFIG_SOC_TWAI_BRP_MIN;CONFIG_SOC_TWAI_CLK_SUPPORT_APB;CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT;CONFIG_SOC_UART_NUM;CONFIG_SOC_UART_HP_NUM;CONFIG_SOC_UART_SUPPORT_APB_CLK;CONFIG_SOC_UART_SUPPORT_REF_TICK;CONFIG_SOC_UART_FIFO_LEN;CONFIG_SOC_UART_BITRATE_MAX;CONFIG_SOC_SPIRAM_SUPPORTED;CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE;CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG;CONFIG_SOC_SHA_ENDIANNESS_BE;CONFIG_SOC_SHA_SUPPORT_SHA1;CONFIG_SOC_SHA_SUPPORT_SHA256;CONFIG_SOC_SHA_SUPPORT_SHA384;CONFIG_SOC_SHA_SUPPORT_SHA512;CONFIG_SOC_MPI_MEM_BLOCKS_NUM;CONFIG_SOC_MPI_OPERATIONS_NUM;CONFIG_SOC_RSA_MAX_BIT_LEN;CONFIG_SOC_AES_SUPPORT_AES_128;CONFIG_SOC_AES_SUPPORT_AES_192;CONFIG_SOC_AES_SUPPORT_AES_256;CONFIG_SOC_SECURE_BOOT_V1;CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS;CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX;CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE;CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP;CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP;CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD;CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD;CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD;CONFIG_SOC_PM_SUPPORT_RC_FAST_PD;CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD;CONFIG_SOC_PM_SUPPORT_MODEM_PD;CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED;CONFIG_SOC_CLK_APLL_SUPPORTED;CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED;CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256;CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION;CONFIG_SOC_CLK_XTAL32K_SUPPORTED;CONFIG_SOC_SDMMC_USE_IOMUX;CONFIG_SOC_SDMMC_NUM_SLOTS;CONFIG_SOC_WIFI_WAPI_SUPPORT;CONFIG_SOC_WIFI_CSI_SUPPORT;CONFIG_SOC_WIFI_MESH_SUPPORT;CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW;CONFIG_SOC_WIFI_NAN_SUPPORT;CONFIG_SOC_BLE_SUPPORTED;CONFIG_SOC_BLE_MESH_SUPPORTED;CONFIG_SOC_BT_CLASSIC_SUPPORTED;CONFIG_SOC_BLUFI_SUPPORTED;CONFIG_SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED;CONFIG_SOC_ULP_HAS_ADC;CONFIG_SOC_PHY_COMBO_MODULE;CONFIG_SOC_EMAC_RMII_CLK_OUT_INTERNAL_LOOPBACK;CONFIG_IDF_CMAKE;CONFIG_IDF_TOOLCHAIN;CONFIG_IDF_TARGET_ARCH_XTENSA;CONFIG_IDF_TARGET_ARCH;CONFIG_IDF_TARGET;CONFIG_IDF_INIT_VERSION;CONFIG_IDF_TARGET_ESP32;CONFIG_IDF_FIRMWARE_CHIP_ID;CONFIG_APP_BUILD_TYPE_APP_2NDBOOT;CONFIG_APP_BUILD_TYPE_RAM;CONFIG_APP_BUILD_TYPE_ELF_RAM;CONFIG_APP_BUILD_GENERATE_BINARIES;CONFIG_APP_BUILD_BOOTLOADER;CONFIG_APP_BUILD_USE_FLASH_SECTIONS;CONFIG_APP_REPRODUCIBLE_BUILD;CONFIG_APP_NO_BLOBS;CONFIG_NO_BLOBS;CONFIG_ESP32_NO_BLOBS;CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS;CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS;CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS;CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS;CONFIG_BOOTLOADER_COMPILE_TIME_DATE;CONFIG_BOOTLOADER_PROJECT_VER;CONFIG_BOOTLOADER_OFFSET_IN_FLASH;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_NONE;CONFIG_LOG_BOOTLOADER_LEVEL_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_ERROR;CONFIG_LOG_BOOTLOADER_LEVEL_ERROR;CONFIG_BOOTLOADER_LOG_LEVEL_WARN;CONFIG_LOG_BOOTLOADER_LEVEL_WARN;CONFIG_BOOTLOADER_LOG_LEVEL_INFO;CONFIG_LOG_BOOTLOADER_LEVEL_INFO;CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG;CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG;CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE;CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE;CONFIG_BOOTLOADER_LOG_LEVEL;CONFIG_LOG_BOOTLOADER_LEVEL;CONFIG_BOOTLOADER_FLASH_DC_AWARE;CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT;CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_8V;CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V;CONFIG_BOOTLOADER_FACTORY_RESET;CONFIG_BOOTLOADER_APP_TEST;CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE;CONFIG_BOOTLOADER_WDT_ENABLE;CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE;CONFIG_BOOTLOADER_WDT_TIME_MS;CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE;CONFIG_APP_ROLLBACK_ENABLE;CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP;CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON;CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS;CONFIG_BOOTLOADER_RESERVE_RTC_SIZE;CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC;CONFIG_SECURE_BOOT_V1_SUPPORTED;CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT;CONFIG_SECURE_BOOT;CONFIG_SECURE_FLASH_ENC_ENABLED;CONFIG_FLASH_ENCRYPTION_ENABLED;CONFIG_APP_COMPILE_TIME_DATE;CONFIG_APP_EXCLUDE_PROJECT_VER_VAR;CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR;CONFIG_APP_PROJECT_VER_FROM_CONFIG;CONFIG_APP_RETRIEVE_LEN_ELF_SHA;CONFIG_ESP_ROM_HAS_CRC_LE;CONFIG_ESP_ROM_HAS_CRC_BE;CONFIG_ESP_ROM_HAS_MZ_CRC32;CONFIG_ESP_ROM_HAS_JPEG_DECODE;CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH;CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND;CONFIG_ESP_ROM_HAS_NEWLIB;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT;CONFIG_ESP_ROM_HAS_NEWLIB_32BIT_TIME;CONFIG_ESP_ROM_HAS_SW_FLOAT;CONFIG_ESP_ROM_USB_OTG_NUM;CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM;CONFIG_ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB;CONFIG_ESPTOOLPY_NO_STUB;CONFIG_ESPTOOLPY_FLASHMODE_QIO;CONFIG_FLASHMODE_QIO;CONFIG_ESPTOOLPY_FLASHMODE_QOUT;CONFIG_FLASHMODE_QOUT;CONFIG_ESPTOOLPY_FLASHMODE_DIO;CONFIG_FLASHMODE_DIO;CONFIG_ESPTOOLPY_FLASHMODE_DOUT;CONFIG_FLASHMODE_DOUT;CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR;CONFIG_ESPTOOLPY_FLASHMODE;CONFIG_ESPTOOLPY_FLASHFREQ_80M;CONFIG_ESPTOOLPY_FLASHFREQ_40M;CONFIG_ESPTOOLPY_FLASHFREQ_26M;CONFIG_ESPTOOLPY_FLASHFREQ_20M;CONFIG_ESPTOOLPY_FLASHFREQ;CONFIG_ESPTOOLPY_FLASHSIZE_1MB;CONFIG_ESPTOOLPY_FLASHSIZE_2MB;CONFIG_ESPTOOLPY_FLASHSIZE_4MB;CONFIG_ESPTOOLPY_FLASHSIZE_8MB;CONFIG_ESPTOOLPY_FLASHSIZE_16MB;CONFIG_ESPTOOLPY_FLASHSIZE_32MB;CONFIG_ESPTOOLPY_FLASHSIZE_64MB;CONFIG_ESPTOOLPY_FLASHSIZE_128MB;CONFIG_ESPTOOLPY_FLASHSIZE;CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE;CONFIG_ESPTOOLPY_BEFORE_RESET;CONFIG_ESPTOOLPY_BEFORE_NORESET;CONFIG_ESPTOOLPY_BEFORE;CONFIG_ESPTOOLPY_AFTER_RESET;CONFIG_ESPTOOLPY_AFTER_NORESET;CONFIG_ESPTOOLPY_AFTER;CONFIG_ESPTOOLPY_MONITOR_BAUD;CONFIG_MONITOR_BAUD;CONFIG_PARTITION_TABLE_SINGLE_APP;CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE;CONFIG_PARTITION_TABLE_TWO_OTA;CONFIG_PARTITION_TABLE_CUSTOM;CONFIG_PARTITION_TABLE_CUSTOM_FILENAME;CONFIG_PARTITION_TABLE_FILENAME;CONFIG_PARTITION_TABLE_OFFSET;CONFIG_PARTITION_TABLE_MD5;CONFIG_COMPILER_OPTIMIZATION_DEBUG;CONFIG_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_DEFAULT;CONFIG_COMPILER_OPTIMIZATION_SIZE;CONFIG_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_PERF;CONFIG_COMPILER_OPTIMIZATION_NONE;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE;CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE;CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED;CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB;CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT;CONFIG_COMPILER_HIDE_PATHS_MACROS;CONFIG_COMPILER_CXX_EXCEPTIONS;CONFIG_CXX_EXCEPTIONS;CONFIG_COMPILER_CXX_RTTI;CONFIG_COMPILER_STACK_CHECK_MODE_NONE;CONFIG_STACK_CHECK_NONE;CONFIG_COMPILER_STACK_CHECK_MODE_NORM;CONFIG_STACK_CHECK_NORM;CONFIG_COMPILER_STACK_CHECK_MODE_STRONG;CONFIG_STACK_CHECK_STRONG;CONFIG_COMPILER_STACK_CHECK_MODE_ALL;CONFIG_STACK_CHECK_ALL;CONFIG_COMPILER_WARN_WRITE_STRINGS;CONFIG_WARN_WRITE_STRINGS;CONFIG_COMPILER_DISABLE_GCC12_WARNINGS;CONFIG_COMPILER_DISABLE_GCC13_WARNINGS;CONFIG_COMPILER_DUMP_RTL_FILES;CONFIG_COMPILER_RT_LIB_GCCLIB;CONFIG_COMPILER_RT_LIB_NAME;CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING;CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE;CONFIG_APPTRACE_DEST_JTAG;CONFIG_ESP32_APPTRACE_DEST_TRAX;CONFIG_APPTRACE_DEST_NONE;CONFIG_ESP32_APPTRACE_DEST_NONE;CONFIG_APPTRACE_DEST_UART1;CONFIG_APPTRACE_DEST_UART2;CONFIG_APPTRACE_DEST_UART_NONE;CONFIG_APPTRACE_UART_TASK_PRIO;CONFIG_APPTRACE_LOCK_ENABLE;CONFIG_ESP32_APPTRACE_LOCK_ENABLE;CONFIG_BT_ENABLED;CONFIG_BT_ALARM_MAX_NUM;CONFIG_CONSOLE_SORTED_HELP;CONFIG_TWAI_ISR_IN_IRAM;CONFIG_TWAI_ERRATA_FIX_BUS_OFF_REC;CONFIG_TWAI_ERRATA_FIX_TX_INTR_LOST;CONFIG_TWAI_ERRATA_FIX_RX_FRAME_INVALID;CONFIG_TWAI_ERRATA_FIX_RX_FIFO_CORRUPT;CONFIG_TWAI_ERRATA_FIX_LISTEN_ONLY_DOM;CONFIG_ADC_DISABLE_DAC;CONFIG_ADC2_DISABLE_DAC;CONFIG_ADC_SUPPRESS_DEPRECATE_WARN;CONFIG_ADC_CAL_EFUSE_TP_ENABLE;CONFIG_ADC_CAL_EFUSE_VREF_ENABLE;CONFIG_ADC_CAL_LUT_ENABLE;CONFIG_ADC_CALI_SUPPRESS_DEPRECATE_WARN;CONFIG_DAC_SUPPRESS_DEPRECATE_WARN;CONFIG_MCPWM_SUPPRESS_DEPRECATE_WARN;CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN;CONFIG_RMT_SUPPRESS_DEPRECATE_WARN;CONFIG_I2S_SUPPRESS_DEPRECATE_WARN;CONFIG_PCNT_SUPPRESS_DEPRECATE_WARN;CONFIG_SDM_SUPPRESS_DEPRECATE_WARN;CONFIG_EFUSE_CUSTOM_TABLE;CONFIG_EFUSE_VIRTUAL;CONFIG_EFUSE_CODE_SCHEME_COMPAT_NONE;CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4;CONFIG_EFUSE_CODE_SCHEME_COMPAT_REPEAT;CONFIG_EFUSE_MAX_BLK_LEN;CONFIG_ESP_TLS_USING_MBEDTLS;CONFIG_ESP_TLS_USE_SECURE_ELEMENT;CONFIG_ESP_TLS_CLIENT_SESSION_TICKETS;CONFIG_ESP_TLS_SERVER_SESSION_TICKETS;CONFIG_ESP_TLS_SERVER_CERT_SELECT_HOOK;CONFIG_ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL;CONFIG_ESP_TLS_PSK_VERIFICATION;CONFIG_ESP_TLS_INSECURE;CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM;CONFIG_ADC_CONTINUOUS_ISR_IRAM_SAFE;CONFIG_ADC_CALI_EFUSE_TP_ENABLE;CONFIG_ADC_CALI_EFUSE_VREF_ENABLE;CONFIG_ADC_CALI_LUT_ENABLE;CONFIG_ADC_DISABLE_DAC_OUTPUT;CONFIG_ADC_ENABLE_DEBUG_LOG;CONFIG_ESP_COEX_ENABLED;CONFIG_ESP_COEX_GPIO_DEBUG;CONFIG_ESP_ERR_TO_NAME_LOOKUP;CONFIG_DAC_CTRL_FUNC_IN_IRAM;CONFIG_DAC_ISR_IRAM_SAFE;CONFIG_DAC_ENABLE_DEBUG_LOG;CONFIG_DAC_DMA_AUTO_16BIT_ALIGN;CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL;CONFIG_GPIO_CTRL_FUNC_IN_IRAM;CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM;CONFIG_GPTIMER_CTRL_FUNC_IN_IRAM;CONFIG_GPTIMER_ISR_IRAM_SAFE;CONFIG_GPTIMER_ENABLE_DEBUG_LOG;CONFIG_I2C_ISR_IRAM_SAFE;CONFIG_I2C_ENABLE_DEBUG_LOG;CONFIG_I2S_ISR_IRAM_SAFE;CONFIG_I2S_ENABLE_DEBUG_LOG;CONFIG_LEDC_CTRL_FUNC_IN_IRAM;CONFIG_MCPWM_ISR_IRAM_SAFE;CONFIG_MCPWM_ISR_IN_IRAM;CONFIG_MCPWM_CTRL_FUNC_IN_IRAM;CONFIG_MCPWM_ENABLE_DEBUG_LOG;CONFIG_PCNT_CTRL_FUNC_IN_IRAM;CONFIG_PCNT_ISR_IRAM_SAFE;CONFIG_PCNT_ENABLE_DEBUG_LOG;CONFIG_RMT_ISR_IRAM_SAFE;CONFIG_RMT_RECV_FUNC_IN_IRAM;CONFIG_RMT_ENABLE_DEBUG_LOG;CONFIG_SDM_CTRL_FUNC_IN_IRAM;CONFIG_SDM_ENABLE_DEBUG_LOG;CONFIG_SPI_MASTER_IN_IRAM;CONFIG_SPI_MASTER_ISR_IN_IRAM;CONFIG_SPI_SLAVE_IN_IRAM;CONFIG_SPI_SLAVE_ISR_IN_IRAM;CONFIG_TOUCH_CTRL_FUNC_IN_IRAM;CONFIG_TOUCH_ISR_IRAM_SAFE;CONFIG_TOUCH_ENABLE_DEBUG_LOG;CONFIG_UART_ISR_IN_IRAM;CONFIG_ETH_ENABLED;CONFIG_ETH_USE_ESP32_EMAC;CONFIG_ETH_PHY_INTERFACE_RMII;CONFIG_ETH_RMII_CLK_INPUT;CONFIG_ETH_RMII_CLK_OUTPUT;CONFIG_ETH_RMII_CLK_IN_GPIO;CONFIG_ETH_DMA_BUFFER_SIZE;CONFIG_ETH_DMA_RX_BUFFER_NUM;CONFIG_ETH_DMA_TX_BUFFER_NUM;CONFIG_ETH_IRAM_OPTIMIZATION;CONFIG_ETH_USE_SPI_ETHERNET;CONFIG_ETH_SPI_ETHERNET_DM9051;CONFIG_ETH_SPI_ETHERNET_W5500;CONFIG_ETH_SPI_ETHERNET_KSZ8851SNL;CONFIG_ETH_USE_OPENETH;CONFIG_ETH_TRANSMIT_MUTEX;CONFIG_ESP_EVENT_LOOP_PROFILING;CONFIG_EVENT_LOOP_PROFILING;CONFIG_ESP_EVENT_POST_FROM_ISR;CONFIG_POST_EVENTS_FROM_ISR;CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR;CONFIG_POST_EVENTS_FROM_IRAM_ISR;CONFIG_ESP_GDBSTUB_ENABLED;CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME;CONFIG_ESP_GDBSTUB_SUPPORT_TASKS;CONFIG_GDBSTUB_SUPPORT_TASKS;CONFIG_ESP_GDBSTUB_MAX_TASKS;CONFIG_GDBSTUB_MAX_TASKS;CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS;CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH;CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH;CONFIG_ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT;CONFIG_HTTPD_MAX_REQ_HDR_LEN;CONFIG_HTTPD_MAX_URI_LEN;CONFIG_HTTPD_ERR_RESP_NO_DELAY;CONFIG_HTTPD_PURGE_BUF_LEN;CONFIG_HTTPD_LOG_PURGE_DATA;CONFIG_HTTPD_WS_SUPPORT;CONFIG_HTTPD_QUEUE_WORK_BLOCKING;CONFIG_ESP_HTTPS_OTA_DECRYPT_CB;CONFIG_ESP_HTTPS_OTA_ALLOW_HTTP;CONFIG_OTA_ALLOW_HTTP;CONFIG_ESP_HTTPS_SERVER_ENABLE;CONFIG_ESP32_REV_MIN_0;CONFIG_ESP32_REV_MIN_1;CONFIG_ESP32_REV_MIN_1_1;CONFIG_ESP32_REV_MIN_2;CONFIG_ESP32_REV_MIN_3;CONFIG_ESP32_REV_MIN_3_1;CONFIG_ESP32_REV_MIN;CONFIG_ESP32_REV_MIN_FULL;CONFIG_ESP_REV_MIN_FULL;CONFIG_ESP32_REV_MAX_FULL;CONFIG_ESP_REV_MAX_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP;CONFIG_ESP_MAC_ADDR_UNIVERSE_BT;CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO;CONFIG_TWO_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES;CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP_MAC_IGNORE_MAC_CRC_ERROR;CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC;CONFIG_ESP_SLEEP_POWER_DOWN_FLASH;CONFIG_ESP_SYSTEM_PD_FLASH;CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND;CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU;CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND;CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND;CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY;CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION;CONFIG_ESP_SLEEP_DEBUG;CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS;CONFIG_RTC_CLK_SRC_INT_RC;CONFIG_ESP32_RTC_CLK_SRC_INT_RC;CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC;CONFIG_RTC_CLK_SRC_EXT_CRYS;CONFIG_ESP32_RTC_CLK_SRC_EXT_CRYS;CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_CRYSTAL;CONFIG_RTC_CLK_SRC_EXT_OSC;CONFIG_ESP32_RTC_CLK_SRC_EXT_OSC;CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_OSC;CONFIG_RTC_CLK_SRC_INT_8MD256;CONFIG_ESP32_RTC_CLK_SRC_INT_8MD256;CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_8MD256;CONFIG_RTC_CLK_CAL_CYCLES;CONFIG_ESP32_RTC_CLK_CAL_CYCLES;CONFIG_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_XTAL_FREQ_26;CONFIG_ESP32_XTAL_FREQ_26;CONFIG_XTAL_FREQ_40;CONFIG_ESP32_XTAL_FREQ_40;CONFIG_XTAL_FREQ_AUTO;CONFIG_ESP32_XTAL_FREQ_AUTO;CONFIG_XTAL_FREQ;CONFIG_ESP32_XTAL_FREQ;CONFIG_ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM;CONFIG_LCD_ENABLE_DEBUG_LOG;CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL;CONFIG_ESP_NETIF_TCPIP_LWIP;CONFIG_ESP_NETIF_LOOPBACK;CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API;CONFIG_ESP_NETIF_RECEIVE_REPORT_ERRORS;CONFIG_ESP_NETIF_L2_TAP;CONFIG_ESP_NETIF_BRIDGE_EN;CONFIG_ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF;CONFIG_ESP_PHY_ENABLED;CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE;CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE;CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION;CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION;CONFIG_ESP_PHY_MAX_WIFI_TX_POWER;CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER;CONFIG_ESP_PHY_MAX_TX_POWER;CONFIG_ESP32_PHY_MAX_TX_POWER;CONFIG_ESP_PHY_REDUCE_TX_POWER;CONFIG_REDUCE_PHY_TX_POWER;CONFIG_ESP32_REDUCE_PHY_TX_POWER;CONFIG_ESP_PHY_ENABLE_CERT_TEST;CONFIG_ESP_PHY_RF_CAL_PARTIAL;CONFIG_ESP_PHY_RF_CAL_NONE;CONFIG_ESP_PHY_RF_CAL_FULL;CONFIG_ESP_PHY_CALIBRATION_MODE;CONFIG_ESP_PHY_PLL_TRACK_DEBUG;CONFIG_PM_ENABLE;CONFIG_PM_SLP_IRAM_OPT;CONFIG_SPIRAM;CONFIG_SPIRAM_SUPPORT;CONFIG_ESP32_SPIRAM_SUPPORT;CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80;CONFIG_ESP32_DEFAULT_CPU_FREQ_80;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160;CONFIG_ESP32_DEFAULT_CPU_FREQ_160;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240;CONFIG_ESP32_DEFAULT_CPU_FREQ_240;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32_USE_FIXED_STATIC_RAM_SIZE;CONFIG_ESP_SYSTEM_ESP32_SRAM1_REGION_AS_IRAM;CONFIG_ESP32_TRAX;CONFIG_ESP32_TRACEMEM_RESERVE_DRAM;CONFIG_TRACEMEM_RESERVE_DRAM;CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT;CONFIG_ESP32_PANIC_PRINT_HALT;CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT;CONFIG_ESP32_PANIC_PRINT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT;CONFIG_ESP32_PANIC_SILENT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_GDBSTUB;CONFIG_ESP32_PANIC_GDBSTUB;CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS;CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_STACK_SIZE;CONFIG_MAIN_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1;CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY;CONFIG_ESP_MAIN_TASK_AFFINITY;CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE;CONFIG_ESP_CONSOLE_UART_DEFAULT;CONFIG_CONSOLE_UART_DEFAULT;CONFIG_ESP_CONSOLE_UART_CUSTOM;CONFIG_CONSOLE_UART_CUSTOM;CONFIG_ESP_CONSOLE_NONE;CONFIG_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART;CONFIG_CONSOLE_UART;CONFIG_ESP_CONSOLE_UART_NUM;CONFIG_CONSOLE_UART_NUM;CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM;CONFIG_ESP_CONSOLE_UART_BAUDRATE;CONFIG_CONSOLE_UART_BAUDRATE;CONFIG_ESP_INT_WDT;CONFIG_INT_WDT;CONFIG_ESP_INT_WDT_TIMEOUT_MS;CONFIG_INT_WDT_TIMEOUT_MS;CONFIG_ESP_INT_WDT_CHECK_CPU1;CONFIG_INT_WDT_CHECK_CPU1;CONFIG_ESP_TASK_WDT_EN;CONFIG_ESP_TASK_WDT_INIT;CONFIG_TASK_WDT;CONFIG_ESP_TASK_WDT;CONFIG_ESP_TASK_WDT_PANIC;CONFIG_TASK_WDT_PANIC;CONFIG_ESP_TASK_WDT_TIMEOUT_S;CONFIG_TASK_WDT_TIMEOUT_S;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_ESP_PANIC_HANDLER_IRAM;CONFIG_ESP_DEBUG_STUBS_ENABLE;CONFIG_ESP32_DEBUG_STUBS_ENABLE;CONFIG_ESP_DEBUG_OCDAWARE;CONFIG_ESP32_DEBUG_OCDAWARE;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_5;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4;CONFIG_ESP_BROWNOUT_DET;CONFIG_BROWNOUT_DET;CONFIG_ESP32_BROWNOUT_DET;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0;CONFIG_BROWNOUT_DET_LVL_SEL_0;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1;CONFIG_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2;CONFIG_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3;CONFIG_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4;CONFIG_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5;CONFIG_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6;CONFIG_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7;CONFIG_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP_BROWNOUT_DET_LVL;CONFIG_BROWNOUT_DET_LVL;CONFIG_ESP32_BROWNOUT_DET_LVL;CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE;CONFIG_DISABLE_BASIC_ROM_CONSOLE;CONFIG_ESP_SYSTEM_BROWNOUT_INTR;CONFIG_ESP_IPC_TASK_STACK_SIZE;CONFIG_IPC_TASK_STACK_SIZE;CONFIG_ESP_IPC_USES_CALLERS_PRIORITY;CONFIG_ESP_IPC_ISR_ENABLE;CONFIG_ESP_TIMER_PROFILING;CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER;CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER;CONFIG_ESP_TIMER_TASK_STACK_SIZE;CONFIG_TIMER_TASK_STACK_SIZE;CONFIG_ESP_TIMER_INTERRUPT_LEVEL;CONFIG_ESP_TIMER_SHOW_EXPERIMENTAL;CONFIG_ESP_TIMER_TASK_AFFINITY;CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0;CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0;CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD;CONFIG_ESP_TIMER_IMPL_TG0_LAC;CONFIG_ESP_WIFI_ENABLED;CONFIG_ESP32_WIFI_ENABLED;CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM;CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM;CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM;CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM;CONFIG_ESP_WIFI_STATIC_TX_BUFFER;CONFIG_ESP32_WIFI_STATIC_TX_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER;CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER;CONFIG_ESP_WIFI_TX_BUFFER_TYPE;CONFIG_ESP32_WIFI_TX_BUFFER_TYPE;CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM;CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM;CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF;CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF;CONFIG_ESP_WIFI_CSI_ENABLED;CONFIG_ESP32_WIFI_CSI_ENABLED;CONFIG_ESP_WIFI_AMPDU_TX_ENABLED;CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED;CONFIG_ESP_WIFI_TX_BA_WIN;CONFIG_ESP32_WIFI_TX_BA_WIN;CONFIG_ESP_WIFI_AMPDU_RX_ENABLED;CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED;CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED;CONFIG_ESP_WIFI_RX_BA_WIN;CONFIG_ESP32_WIFI_RX_BA_WIN;CONFIG_ESP32_WIFI_RX_BA_WIN;CONFIG_ESP_WIFI_NVS_ENABLED;CONFIG_ESP32_WIFI_NVS_ENABLED;CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_0;CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_0;CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_1;CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_1;CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN;CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN;CONFIG_ESP_WIFI_MGMT_SBUF_NUM;CONFIG_ESP32_WIFI_MGMT_SBUF_NUM;CONFIG_ESP_WIFI_IRAM_OPT;CONFIG_ESP32_WIFI_IRAM_OPT;CONFIG_ESP_WIFI_EXTRA_IRAM_OPT;CONFIG_ESP_WIFI_RX_IRAM_OPT;CONFIG_ESP32_WIFI_RX_IRAM_OPT;CONFIG_ESP_WIFI_ENABLE_WPA3_SAE;CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE;CONFIG_ESP_WIFI_ENABLE_SAE_PK;CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT;CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA;CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA;CONFIG_ESP_WIFI_SLP_IRAM_OPT;CONFIG_ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME;CONFIG_ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME;CONFIG_ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME;CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE;CONFIG_ESP_WIFI_GMAC_SUPPORT;CONFIG_ESP_WIFI_SOFTAP_SUPPORT;CONFIG_ESP_WIFI_SLP_BEACON_LOST_OPT;CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM;CONFIG_ESP_WIFI_NAN_ENABLE;CONFIG_ESP_WIFI_MBEDTLS_CRYPTO;CONFIG_WPA_MBEDTLS_CRYPTO;CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT;CONFIG_WPA_MBEDTLS_TLS_CLIENT;CONFIG_ESP_WIFI_WAPI_PSK;CONFIG_WPA_WAPI_PSK;CONFIG_ESP_WIFI_11KV_SUPPORT;CONFIG_WPA_11KV_SUPPORT;CONFIG_ESP_WIFI_MBO_SUPPORT;CONFIG_WPA_MBO_SUPPORT;CONFIG_ESP_WIFI_DPP_SUPPORT;CONFIG_WPA_DPP_SUPPORT;CONFIG_ESP_WIFI_11R_SUPPORT;CONFIG_WPA_11R_SUPPORT;CONFIG_ESP_WIFI_WPS_SOFTAP_REGISTRAR;CONFIG_WPA_WPS_SOFTAP_REGISTRAR;CONFIG_ESP_WIFI_WPS_STRICT;CONFIG_WPA_WPS_STRICT;CONFIG_ESP_WIFI_WPS_PASSPHRASE;CONFIG_ESP_WIFI_DEBUG_PRINT;CONFIG_WPA_DEBUG_PRINT;CONFIG_ESP_WIFI_TESTING_OPTIONS;CONFIG_WPA_TESTING_OPTIONS;CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT;CONFIG_ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER;CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH;CONFIG_ESP32_ENABLE_COREDUMP_TO_FLASH;CONFIG_ESP_COREDUMP_ENABLE_TO_UART;CONFIG_ESP32_ENABLE_COREDUMP_TO_UART;CONFIG_ESP_COREDUMP_ENABLE_TO_NONE;CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE;CONFIG_FATFS_VOLUME_COUNT;CONFIG_FATFS_LFN_NONE;CONFIG_FATFS_LFN_HEAP;CONFIG_FATFS_LFN_STACK;CONFIG_FATFS_SECTOR_512;CONFIG_FATFS_SECTOR_4096;CONFIG_FATFS_CODEPAGE_DYNAMIC;CONFIG_FATFS_CODEPAGE_437;CONFIG_FATFS_CODEPAGE_720;CONFIG_FATFS_CODEPAGE_737;CONFIG_FATFS_CODEPAGE_771;CONFIG_FATFS_CODEPAGE_775;CONFIG_FATFS_CODEPAGE_850;CONFIG_FATFS_CODEPAGE_852;CONFIG_FATFS_CODEPAGE_855;CONFIG_FATFS_CODEPAGE_857;CONFIG_FATFS_CODEPAGE_860;CONFIG_FATFS_CODEPAGE_861;CONFIG_FATFS_CODEPAGE_862;CONFIG_FATFS_CODEPAGE_863;CONFIG_FATFS_CODEPAGE_864;CONFIG_FATFS_CODEPAGE_865;CONFIG_FATFS_CODEPAGE_866;CONFIG_FATFS_CODEPAGE_869;CONFIG_FATFS_CODEPAGE_932;CONFIG_FATFS_CODEPAGE_936;CONFIG_FATFS_CODEPAGE_949;CONFIG_FATFS_CODEPAGE_950;CONFIG_FATFS_CODEPAGE;CONFIG_FATFS_FS_LOCK;CONFIG_FATFS_TIMEOUT_MS;CONFIG_FATFS_PER_FILE_CACHE;CONFIG_FATFS_USE_FASTSEEK;CONFIG_FATFS_VFS_FSTAT_BLKSIZE;CONFIG_FATFS_IMMEDIATE_FSYNC;CONFIG_FATFS_USE_LABEL;CONFIG_FATFS_LINK_LOCK;CONFIG_FREERTOS_SMP;CONFIG_FREERTOS_UNICORE;CONFIG_FREERTOS_HZ;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY;CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS;CONFIG_FREERTOS_IDLE_TASK_STACKSIZE;CONFIG_FREERTOS_USE_IDLE_HOOK;CONFIG_FREERTOS_USE_TICK_HOOK;CONFIG_FREERTOS_MAX_TASK_NAME_LEN;CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1;CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY;CONFIG_FREERTOS_TIMER_TASK_PRIORITY;CONFIG_TIMER_TASK_PRIORITY;CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH;CONFIG_TIMER_TASK_STACK_DEPTH;CONFIG_FREERTOS_TIMER_QUEUE_LENGTH;CONFIG_TIMER_QUEUE_LENGTH;CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE;CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES;CONFIG_FREERTOS_USE_TRACE_FACILITY;CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES;CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS;CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG;CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER;CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK;CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS;CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK;CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP;CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK;CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER;CONFIG_FREERTOS_ISR_STACKSIZE;CONFIG_FREERTOS_INTERRUPT_BACKTRACE;CONFIG_FREERTOS_FPU_IN_ISR;CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER;CONFIG_FREERTOS_CORETIMER_0;CONFIG_FREERTOS_CORETIMER_1;CONFIG_FREERTOS_SYSTICK_USES_CCOUNT;CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE;CONFIG_FREERTOS_PORT;CONFIG_FREERTOS_NO_AFFINITY;CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION;CONFIG_FREERTOS_DEBUG_OCDAWARE;CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT;CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH;CONFIG_FREERTOS_NUMBER_OF_CORES;CONFIG_HAL_ASSERTION_EQUALS_SYSTEM;CONFIG_HAL_ASSERTION_DISABLE;CONFIG_HAL_ASSERTION_SILENT;CONFIG_HAL_ASSERTION_SILIENT;CONFIG_HAL_ASSERTION_ENABLE;CONFIG_HAL_DEFAULT_ASSERTION_LEVEL;CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM;CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM;CONFIG_HAL_ECDSA_GEN_SIG_CM;CONFIG_HEAP_POISONING_DISABLED;CONFIG_HEAP_POISONING_LIGHT;CONFIG_HEAP_POISONING_COMPREHENSIVE;CONFIG_HEAP_TRACING_OFF;CONFIG_HEAP_TRACING_STANDALONE;CONFIG_HEAP_TRACING_TOHOST;CONFIG_HEAP_USE_HOOKS;CONFIG_HEAP_TASK_TRACKING;CONFIG_HEAP_ABORT_WHEN_ALLOCATION_FAILS;CONFIG_HEAP_PLACE_FUNCTION_INTO_FLASH;CONFIG_LOG_DEFAULT_LEVEL_NONE;CONFIG_LOG_DEFAULT_LEVEL_ERROR;CONFIG_LOG_DEFAULT_LEVEL_WARN;CONFIG_LOG_DEFAULT_LEVEL_INFO;CONFIG_LOG_DEFAULT_LEVEL_DEBUG;CONFIG_LOG_DEFAULT_LEVEL_VERBOSE;CONFIG_LOG_DEFAULT_LEVEL;CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT;CONFIG_LOG_MAXIMUM_LEVEL_DEBUG;CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE;CONFIG_LOG_MAXIMUM_LEVEL;CONFIG_LOG_MASTER_LEVEL;CONFIG_LOG_COLORS;CONFIG_LOG_TIMESTAMP_SOURCE_RTOS;CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM;CONFIG_LWIP_ENABLE;CONFIG_LWIP_LOCAL_HOSTNAME;CONFIG_LWIP_NETIF_API;CONFIG_LWIP_TCPIP_TASK_PRIO;CONFIG_LWIP_TCPIP_CORE_LOCKING;CONFIG_LWIP_CHECK_THREAD_SAFETY;CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES;CONFIG_LWIP_L2_TO_L3_COPY;CONFIG_L2_TO_L3_COPY;CONFIG_LWIP_IRAM_OPTIMIZATION;CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION;CONFIG_LWIP_TIMERS_ONDEMAND;CONFIG_LWIP_ND6;CONFIG_LWIP_FORCE_ROUTER_FORWARDING;CONFIG_LWIP_MAX_SOCKETS;CONFIG_LWIP_USE_ONLY_LWIP_SELECT;CONFIG_LWIP_SO_LINGER;CONFIG_LWIP_SO_REUSE;CONFIG_LWIP_SO_REUSE_RXTOALL;CONFIG_LWIP_SO_RCVBUF;CONFIG_LWIP_NETBUF_RECVINFO;CONFIG_LWIP_IP_DEFAULT_TTL;CONFIG_LWIP_IP4_FRAG;CONFIG_LWIP_IP6_FRAG;CONFIG_LWIP_IP4_REASSEMBLY;CONFIG_LWIP_IP6_REASSEMBLY;CONFIG_LWIP_IP_REASS_MAX_PBUFS;CONFIG_LWIP_IP_FORWARD;CONFIG_LWIP_STATS;CONFIG_LWIP_ESP_GRATUITOUS_ARP;CONFIG_ESP_GRATUITOUS_ARP;CONFIG_LWIP_GARP_TMR_INTERVAL;CONFIG_GARP_TMR_INTERVAL;CONFIG_LWIP_ESP_MLDV6_REPORT;CONFIG_LWIP_MLDV6_TMR_INTERVAL;CONFIG_LWIP_TCPIP_RECVMBOX_SIZE;CONFIG_TCPIP_RECVMBOX_SIZE;CONFIG_LWIP_DHCP_DOES_ARP_CHECK;CONFIG_LWIP_DHCP_DISABLE_CLIENT_ID;CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID;CONFIG_LWIP_DHCP_RESTORE_LAST_IP;CONFIG_LWIP_DHCP_OPTIONS_LEN;CONFIG_LWIP_NUM_NETIF_CLIENT_DATA;CONFIG_LWIP_DHCP_COARSE_TIMER_SECS;CONFIG_LWIP_DHCPS;CONFIG_LWIP_DHCPS_LEASE_UNIT;CONFIG_LWIP_DHCPS_MAX_STATION_NUM;CONFIG_LWIP_DHCPS_STATIC_ENTRIES;CONFIG_LWIP_AUTOIP;CONFIG_LWIP_IPV4;CONFIG_LWIP_IPV6;CONFIG_LWIP_IPV6_AUTOCONFIG;CONFIG_LWIP_IPV6_NUM_ADDRESSES;CONFIG_LWIP_IPV6_FORWARD;CONFIG_LWIP_NETIF_STATUS_CALLBACK;CONFIG_LWIP_NETIF_LOOPBACK;CONFIG_LWIP_LOOPBACK_MAX_PBUFS;CONFIG_LWIP_MAX_ACTIVE_TCP;CONFIG_LWIP_MAX_LISTENING_TCP;CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION;CONFIG_LWIP_TCP_MAXRTX;CONFIG_TCP_MAXRTX;CONFIG_LWIP_TCP_SYNMAXRTX;CONFIG_TCP_SYNMAXRTX;CONFIG_LWIP_TCP_MSS;CONFIG_TCP_MSS;CONFIG_LWIP_TCP_TMR_INTERVAL;CONFIG_LWIP_TCP_MSL;CONFIG_TCP_MSL;CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT;CONFIG_LWIP_TCP_SND_BUF_DEFAULT;CONFIG_TCP_SND_BUF_DEFAULT;CONFIG_LWIP_TCP_WND_DEFAULT;CONFIG_TCP_WND_DEFAULT;CONFIG_LWIP_TCP_RECVMBOX_SIZE;CONFIG_TCP_RECVMBOX_SIZE;CONFIG_LWIP_TCP_ACCEPTMBOX_SIZE;CONFIG_LWIP_TCP_QUEUE_OOSEQ;CONFIG_TCP_QUEUE_OOSEQ;CONFIG_LWIP_TCP_OOSEQ_TIMEOUT;CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS;CONFIG_LWIP_TCP_SACK_OUT;CONFIG_LWIP_TCP_OVERSIZE_MSS;CONFIG_TCP_OVERSIZE_MSS;CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS;CONFIG_TCP_OVERSIZE_QUARTER_MSS;CONFIG_LWIP_TCP_OVERSIZE_DISABLE;CONFIG_TCP_OVERSIZE_DISABLE;CONFIG_LWIP_TCP_RTO_TIME;CONFIG_LWIP_MAX_UDP_PCBS;CONFIG_LWIP_UDP_RECVMBOX_SIZE;CONFIG_UDP_RECVMBOX_SIZE;CONFIG_LWIP_CHECKSUM_CHECK_IP;CONFIG_LWIP_CHECKSUM_CHECK_UDP;CONFIG_LWIP_CHECKSUM_CHECK_ICMP;CONFIG_LWIP_TCPIP_TASK_STACK_SIZE;CONFIG_TCPIP_TASK_STACK_SIZE;CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY;CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY;CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU0;CONFIG_TCPIP_TASK_AFFINITY_CPU0;CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU1;CONFIG_TCPIP_TASK_AFFINITY_CPU1;CONFIG_LWIP_TCPIP_TASK_AFFINITY;CONFIG_TCPIP_TASK_AFFINITY;CONFIG_LWIP_IPV6_ND6_NUM_PREFIXES;CONFIG_LWIP_IPV6_ND6_NUM_ROUTERS;CONFIG_LWIP_IPV6_ND6_NUM_DESTINATIONS;CONFIG_LWIP_PPP_SUPPORT;CONFIG_PPP_SUPPORT;CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE;CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS;CONFIG_LWIP_SLIP_SUPPORT;CONFIG_LWIP_ICMP;CONFIG_LWIP_MULTICAST_PING;CONFIG_LWIP_BROADCAST_PING;CONFIG_LWIP_MAX_RAW_PCBS;CONFIG_LWIP_SNTP_MAX_SERVERS;CONFIG_LWIP_DHCP_GET_NTP_SRV;CONFIG_LWIP_SNTP_UPDATE_DELAY;CONFIG_LWIP_SNTP_STARTUP_DELAY;CONFIG_LWIP_SNTP_MAXIMUM_STARTUP_DELAY;CONFIG_LWIP_DNS_MAX_HOST_IP;CONFIG_LWIP_DNS_MAX_SERVERS;CONFIG_LWIP_FALLBACK_DNS_SERVER_SUPPORT;CONFIG_LWIP_DNS_SETSERVER_WITH_NETIF;CONFIG_LWIP_BRIDGEIF_MAX_PORTS;CONFIG_LWIP_ESP_LWIP_ASSERT;CONFIG_LWIP_HOOK_TCP_ISN_NONE;CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT;CONFIG_LWIP_HOOK_TCP_ISN_CUSTOM;CONFIG_LWIP_HOOK_IP6_ROUTE_NONE;CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT;CONFIG_LWIP_HOOK_IP6_ROUTE_CUSTOM;CONFIG_LWIP_HOOK_ND6_GET_GW_NONE;CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT;CONFIG_LWIP_HOOK_ND6_GET_GW_CUSTOM;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM;CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_NONE;CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM;CONFIG_LWIP_HOOK_IP6_INPUT_NONE;CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT;CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM;CONFIG_LWIP_DEBUG;CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC;CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC;CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC;CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN;CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN;CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN;CONFIG_MBEDTLS_DYNAMIC_BUFFER;CONFIG_MBEDTLS_DEBUG;CONFIG_MBEDTLS_SSL_PROTO_TLS1_3;CONFIG_MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH;CONFIG_MBEDTLS_X509_TRUSTED_CERT_CALLBACK;CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION;CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE;CONFIG_MBEDTLS_PKCS7_C;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE;CONFIG_MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS;CONFIG_MBEDTLS_ECP_RESTARTABLE;CONFIG_MBEDTLS_CMAC_C;CONFIG_MBEDTLS_HARDWARE_AES;CONFIG_MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER;CONFIG_MBEDTLS_HARDWARE_MPI;CONFIG_MBEDTLS_LARGE_KEY_SOFTWARE_MPI;CONFIG_MBEDTLS_HARDWARE_SHA;CONFIG_MBEDTLS_ROM_MD5;CONFIG_MBEDTLS_ATCA_HW_ECDSA_SIGN;CONFIG_MBEDTLS_ATCA_HW_ECDSA_VERIFY;CONFIG_MBEDTLS_HAVE_TIME;CONFIG_MBEDTLS_PLATFORM_TIME_ALT;CONFIG_MBEDTLS_HAVE_TIME_DATE;CONFIG_MBEDTLS_ECDSA_DETERMINISTIC;CONFIG_MBEDTLS_SHA512_C;CONFIG_MBEDTLS_SHA3_C;CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT;CONFIG_MBEDTLS_TLS_SERVER_ONLY;CONFIG_MBEDTLS_TLS_CLIENT_ONLY;CONFIG_MBEDTLS_TLS_DISABLED;CONFIG_MBEDTLS_TLS_SERVER;CONFIG_MBEDTLS_TLS_CLIENT;CONFIG_MBEDTLS_TLS_ENABLED;CONFIG_MBEDTLS_PSK_MODES;CONFIG_MBEDTLS_KEY_EXCHANGE_RSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA;CONFIG_MBEDTLS_SSL_RENEGOTIATION;CONFIG_MBEDTLS_SSL_PROTO_TLS1_2;CONFIG_MBEDTLS_SSL_PROTO_GMTSSL1_1;CONFIG_MBEDTLS_SSL_PROTO_DTLS;CONFIG_MBEDTLS_SSL_ALPN;CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS;CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS;CONFIG_MBEDTLS_AES_C;CONFIG_MBEDTLS_CAMELLIA_C;CONFIG_MBEDTLS_DES_C;CONFIG_MBEDTLS_BLOWFISH_C;CONFIG_MBEDTLS_XTEA_C;CONFIG_MBEDTLS_CCM_C;CONFIG_MBEDTLS_GCM_C;CONFIG_MBEDTLS_NIST_KW_C;CONFIG_MBEDTLS_RIPEMD160_C;CONFIG_MBEDTLS_PEM_PARSE_C;CONFIG_MBEDTLS_PEM_WRITE_C;CONFIG_MBEDTLS_X509_CRL_PARSE_C;CONFIG_MBEDTLS_X509_CSR_PARSE_C;CONFIG_MBEDTLS_ECP_C;CONFIG_MBEDTLS_DHM_C;CONFIG_MBEDTLS_ECDH_C;CONFIG_MBEDTLS_ECDSA_C;CONFIG_MBEDTLS_ECJPAKE_C;CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED;CONFIG_MBEDTLS_ECP_NIST_OPTIM;CONFIG_MBEDTLS_ECP_FIXED_POINT_OPTIM;CONFIG_MBEDTLS_POLY1305_C;CONFIG_MBEDTLS_CHACHA20_C;CONFIG_MBEDTLS_HKDF_C;CONFIG_MBEDTLS_THREADING_C;CONFIG_MBEDTLS_ERROR_STRINGS;CONFIG_MBEDTLS_FS_IO;CONFIG_MQTT_PROTOCOL_311;CONFIG_MQTT_PROTOCOL_5;CONFIG_MQTT_TRANSPORT_SSL;CONFIG_MQTT_TRANSPORT_WEBSOCKET;CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE;CONFIG_MQTT_MSG_ID_INCREMENTAL;CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED;CONFIG_MQTT_REPORT_DELETED_MESSAGES;CONFIG_MQTT_USE_CUSTOM_CONFIG;CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED;CONFIG_MQTT_CUSTOM_OUTBOX;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR;CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDIN_LINE_ENDING_LF;CONFIG_NEWLIB_STDIN_LINE_ENDING_CR;CONFIG_NEWLIB_NANO_FORMAT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC;CONFIG_ESP32_TIME_SYSCALL_USE_RTC;CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE;CONFIG_ESP32_TIME_SYSCALL_USE_NONE;CONFIG_NVS_ASSERT_ERROR_CHECK;CONFIG_NVS_LEGACY_DUP_KEYS_COMPATIBILITY;CONFIG_OPENTHREAD_ENABLED;CONFIG_OPENTHREAD_NETWORK_NAME;CONFIG_OPENTHREAD_MESH_LOCAL_PREFIX;CONFIG_OPENTHREAD_NETWORK_CHANNEL;CONFIG_OPENTHREAD_NETWORK_PANID;CONFIG_OPENTHREAD_NETWORK_EXTPANID;CONFIG_OPENTHREAD_NETWORK_MASTERKEY;CONFIG_OPENTHREAD_NETWORK_PSKC;CONFIG_OPENTHREAD_XTAL_ACCURACY;CONFIG_OPENTHREAD_SPINEL_ONLY;CONFIG_OPENTHREAD_RX_ON_WHEN_IDLE;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2;CONFIG_PTHREAD_TASK_PRIO_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT;CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT;CONFIG_PTHREAD_STACK_MIN;CONFIG_ESP32_PTHREAD_STACK_MIN;CONFIG_PTHREAD_DEFAULT_CORE_NO_AFFINITY;CONFIG_ESP32_DEFAULT_PTHREAD_CORE_NO_AFFINITY;CONFIG_PTHREAD_DEFAULT_CORE_0;CONFIG_ESP32_DEFAULT_PTHREAD_CORE_0;CONFIG_PTHREAD_DEFAULT_CORE_1;CONFIG_ESP32_DEFAULT_PTHREAD_CORE_1;CONFIG_PTHREAD_TASK_CORE_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT;CONFIG_PTHREAD_TASK_NAME_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT;CONFIG_MMU_PAGE_SIZE_64KB;CONFIG_MMU_PAGE_MODE;CONFIG_MMU_PAGE_SIZE;CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC;CONFIG_SPI_FLASH_BROWNOUT_RESET;CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US;CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND;CONFIG_SPI_FLASH_VERIFY_WRITE;CONFIG_SPI_FLASH_ENABLE_COUNTERS;CONFIG_SPI_FLASH_ROM_DRIVER_PATCH;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED;CONFIG_SPI_FLASH_SHARE_SPI1_BUS;CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE;CONFIG_SPI_FLASH_YIELD_DURING_ERASE;CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS;CONFIG_SPI_FLASH_ERASE_YIELD_TICKS;CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE;CONFIG_SPI_FLASH_SIZE_OVERRIDE;CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED;CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST;CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED;CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP;CONFIG_SPI_FLASH_SUPPORT_GD_CHIP;CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP;CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP;CONFIG_SPI_FLASH_SUPPORT_TH_CHIP;CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE;CONFIG_SPIFFS_MAX_PARTITIONS;CONFIG_SPIFFS_CACHE;CONFIG_SPIFFS_CACHE_WR;CONFIG_SPIFFS_CACHE_STATS;CONFIG_SPIFFS_PAGE_CHECK;CONFIG_SPIFFS_GC_MAX_RUNS;CONFIG_SPIFFS_GC_STATS;CONFIG_SPIFFS_PAGE_SIZE;CONFIG_SPIFFS_OBJ_NAME_LEN;CONFIG_SPIFFS_FOLLOW_SYMLINKS;CONFIG_SPIFFS_USE_MAGIC;CONFIG_SPIFFS_USE_MAGIC_LENGTH;CONFIG_SPIFFS_META_LENGTH;CONFIG_SPIFFS_USE_MTIME;CONFIG_SPIFFS_DBG;CONFIG_SPIFFS_API_DBG;CONFIG_SPIFFS_GC_DBG;CONFIG_SPIFFS_CACHE_DBG;CONFIG_SPIFFS_CHECK_DBG;CONFIG_SPIFFS_TEST_VISUALISATION;CONFIG_WS_TRANSPORT;CONFIG_WS_BUFFER_SIZE;CONFIG_WS_DYNAMIC_BUFFER;CONFIG_ULP_COPROC_ENABLED;CONFIG_ESP32_ULP_COPROC_ENABLED;CONFIG_UNITY_ENABLE_FLOAT;CONFIG_UNITY_ENABLE_DOUBLE;CONFIG_UNITY_ENABLE_64BIT;CONFIG_UNITY_ENABLE_COLOR;CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER;CONFIG_UNITY_ENABLE_FIXTURE;CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL;CONFIG_VFS_SUPPORT_IO;CONFIG_VFS_SUPPORT_DIR;CONFIG_VFS_SUPPORT_SELECT;CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT;CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT;CONFIG_VFS_SELECT_IN_RAM;CONFIG_VFS_SUPPORT_TERMIOS;CONFIG_SUPPORT_TERMIOS;CONFIG_VFS_MAX_COUNT;CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS;CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS;CONFIG_WL_SECTOR_SIZE_512;CONFIG_WL_SECTOR_SIZE_4096;CONFIG_WL_SECTOR_SIZE;CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES;CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT;CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN;CONFIG_WIFI_PROV_STA_FAST_SCAN;CONFIG_IDF_EXPERIMENTAL_FEATURES)
# List of deprecated options for backward compatibility
set(CONFIG_APP_BUILD_TYPE_ELF_RAM "")
set(CONFIG_NO_BLOBS "")
set(CONFIG_ESP32_NO_BLOBS "")
set(CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS "")
set(CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_NONE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_ERROR "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_WARN "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_INFO "y")
set(CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL "3")
set(CONFIG_APP_ROLLBACK_ENABLE "")
set(CONFIG_FLASH_ENCRYPTION_ENABLED "")
set(CONFIG_FLASHMODE_QIO "")
set(CONFIG_FLASHMODE_QOUT "")
set(CONFIG_FLASHMODE_DIO "y")
set(CONFIG_FLASHMODE_DOUT "")
set(CONFIG_MONITOR_BAUD "115200")
set(CONFIG_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEFAULT "y")
set(CONFIG_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED "y")
set(CONFIG_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED "")
set(CONFIG_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_CXX_EXCEPTIONS "")
set(CONFIG_STACK_CHECK_NONE "y")
set(CONFIG_STACK_CHECK_NORM "")
set(CONFIG_STACK_CHECK_STRONG "")
set(CONFIG_STACK_CHECK_ALL "")
set(CONFIG_WARN_WRITE_STRINGS "")
set(CONFIG_ESP32_APPTRACE_DEST_TRAX "")
set(CONFIG_ESP32_APPTRACE_DEST_NONE "y")
set(CONFIG_ESP32_APPTRACE_LOCK_ENABLE "y")
set(CONFIG_ADC2_DISABLE_DAC "y")
set(CONFIG_MCPWM_ISR_IN_IRAM "")
set(CONFIG_EVENT_LOOP_PROFILING "")
set(CONFIG_POST_EVENTS_FROM_ISR "y")
set(CONFIG_POST_EVENTS_FROM_IRAM_ISR "y")
set(CONFIG_GDBSTUB_SUPPORT_TASKS "y")
set(CONFIG_GDBSTUB_MAX_TASKS "32")
set(CONFIG_OTA_ALLOW_HTTP "")
set(CONFIG_TWO_UNIVERSAL_MAC_ADDRESS "")
set(CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS "y")
set(CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS "4")
set(CONFIG_ESP_SYSTEM_PD_FLASH "")
set(CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP32_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC "y")
set(CONFIG_ESP32_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_CRYSTAL "")
set(CONFIG_ESP32_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_OSC "")
set(CONFIG_ESP32_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_8MD256 "")
set(CONFIG_ESP32_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_ESP32_XTAL_FREQ_26 "")
set(CONFIG_ESP32_XTAL_FREQ_40 "y")
set(CONFIG_ESP32_XTAL_FREQ_AUTO "")
set(CONFIG_ESP32_XTAL_FREQ "40")
set(CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE "y")
set(CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION "")
set(CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER "20")
set(CONFIG_ESP32_PHY_MAX_TX_POWER "20")
set(CONFIG_REDUCE_PHY_TX_POWER "")
set(CONFIG_ESP32_REDUCE_PHY_TX_POWER "")
set(CONFIG_SPIRAM_SUPPORT "")
set(CONFIG_ESP32_SPIRAM_SUPPORT "")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_80 "")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_160 "y")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_240 "")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ "160")
set(CONFIG_TRACEMEM_RESERVE_DRAM "0x0")
set(CONFIG_ESP32_PANIC_PRINT_HALT "")
set(CONFIG_ESP32_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP32_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP32_PANIC_GDBSTUB "")
set(CONFIG_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_CONSOLE_UART_DEFAULT "y")
set(CONFIG_CONSOLE_UART_CUSTOM "")
set(CONFIG_CONSOLE_UART_NONE "")
set(CONFIG_ESP_CONSOLE_UART_NONE "")
set(CONFIG_CONSOLE_UART "y")
set(CONFIG_CONSOLE_UART_NUM "0")
set(CONFIG_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_INT_WDT "y")
set(CONFIG_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_TASK_WDT "y")
set(CONFIG_ESP_TASK_WDT "y")
set(CONFIG_TASK_WDT_PANIC "")
set(CONFIG_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP32_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP32_DEBUG_OCDAWARE "y")
set(CONFIG_BROWNOUT_DET "y")
set(CONFIG_ESP32_BROWNOUT_DET "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_BROWNOUT_DET_LVL "0")
set(CONFIG_ESP32_BROWNOUT_DET_LVL "0")
set(CONFIG_DISABLE_BASIC_ROM_CONSOLE "")
set(CONFIG_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_TIMER_TASK_STACK_SIZE "3584")
set(CONFIG_ESP32_WIFI_ENABLED "y")
set(CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM "10")
set(CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM "32")
set(CONFIG_ESP32_WIFI_STATIC_TX_BUFFER "")
set(CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER "y")
set(CONFIG_ESP32_WIFI_TX_BUFFER_TYPE "1")
set(CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM "32")
set(CONFIG_ESP32_WIFI_CSI_ENABLED "")
set(CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED "y")
set(CONFIG_ESP32_WIFI_TX_BA_WIN "6")
set(CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED "y")
set(CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED "y")
set(CONFIG_ESP32_WIFI_RX_BA_WIN "6")
set(CONFIG_ESP32_WIFI_RX_BA_WIN "6")
set(CONFIG_ESP32_WIFI_NVS_ENABLED "y")
set(CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_0 "y")
set(CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_1 "")
set(CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN "752")
set(CONFIG_ESP32_WIFI_MGMT_SBUF_NUM "32")
set(CONFIG_ESP32_WIFI_IRAM_OPT "y")
set(CONFIG_ESP32_WIFI_RX_IRAM_OPT "y")
set(CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE "y")
set(CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA "y")
set(CONFIG_WPA_MBEDTLS_CRYPTO "y")
set(CONFIG_WPA_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_WPA_WAPI_PSK "")
set(CONFIG_WPA_11KV_SUPPORT "")
set(CONFIG_WPA_MBO_SUPPORT "")
set(CONFIG_WPA_DPP_SUPPORT "")
set(CONFIG_WPA_11R_SUPPORT "")
set(CONFIG_WPA_WPS_SOFTAP_REGISTRAR "")
set(CONFIG_WPA_WPS_STRICT "")
set(CONFIG_WPA_DEBUG_PRINT "")
set(CONFIG_WPA_TESTING_OPTIONS "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_FLASH "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_UART "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE "y")
set(CONFIG_TIMER_TASK_PRIORITY "1")
set(CONFIG_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_TIMER_QUEUE_LENGTH "10")
set(CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK "")
set(CONFIG_HAL_ASSERTION_SILIENT "")
set(CONFIG_L2_TO_L3_COPY "")
set(CONFIG_ESP_GRATUITOUS_ARP "y")
set(CONFIG_GARP_TMR_INTERVAL "60")
set(CONFIG_TCPIP_RECVMBOX_SIZE "32")
set(CONFIG_TCP_MAXRTX "12")
set(CONFIG_TCP_SYNMAXRTX "12")
set(CONFIG_TCP_MSS "1440")
set(CONFIG_TCP_MSL "60000")
set(CONFIG_TCP_SND_BUF_DEFAULT "5760")
set(CONFIG_TCP_WND_DEFAULT "5760")
set(CONFIG_TCP_RECVMBOX_SIZE "6")
set(CONFIG_TCP_QUEUE_OOSEQ "y")
set(CONFIG_TCP_OVERSIZE_MSS "y")
set(CONFIG_TCP_OVERSIZE_QUARTER_MSS "")
set(CONFIG_TCP_OVERSIZE_DISABLE "")
set(CONFIG_UDP_RECVMBOX_SIZE "6")
set(CONFIG_TCPIP_TASK_STACK_SIZE "3072")
set(CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY "y")
set(CONFIG_TCPIP_TASK_AFFINITY_CPU0 "")
set(CONFIG_TCPIP_TASK_AFFINITY_CPU1 "")
set(CONFIG_TCPIP_TASK_AFFINITY "0x7fffffff")
set(CONFIG_PPP_SUPPORT "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1 "y")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_HRT "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_FRC1 "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_NONE "")
set(CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT "5")
set(CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT "3072")
set(CONFIG_ESP32_PTHREAD_STACK_MIN "768")
set(CONFIG_ESP32_DEFAULT_PTHREAD_CORE_NO_AFFINITY "y")
set(CONFIG_ESP32_DEFAULT_PTHREAD_CORE_0 "")
set(CONFIG_ESP32_DEFAULT_PTHREAD_CORE_1 "")
set(CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT "-1")
set(CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT "pthread")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS "y")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED "")
set(CONFIG_ESP32_ULP_COPROC_ENABLED "")
set(CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT "y")
set(CONFIG_SUPPORT_TERMIOS "y")
set(CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS "1")
