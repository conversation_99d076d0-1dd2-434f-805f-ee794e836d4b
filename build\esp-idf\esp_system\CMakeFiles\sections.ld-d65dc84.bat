@echo off
cd /D D:\ESP32_Projects\hello_world\build\esp-idf\esp_system || (set FAIL_LINE=2& goto :ABORT)
d:\esp32-idf-ahy\5.3.2\python_env\idf5.3_py3.11_env\Scripts\python.exe D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/tools/ldgen/ldgen.py --config D:/ESP32_Projects/hello_world/sdkconfig --fragments-list D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/xtensa/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_gpio/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_pm/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_mm/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/spi_flash/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_system/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_system/app.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_common/common.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_common/soc.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_rom/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/hal/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/log/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/heap/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/soc/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_hw_support/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_hw_support/dma/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_hw_support/ldo/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/freertos/linker_common.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/freertos/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/newlib/newlib.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/newlib/system_libs.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_gptimer/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_ringbuf/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_uart/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/app_trace/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_event/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_pcnt/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_spi/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_mcpwm/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_ana_cmpr/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_dac/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_rmt/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_sdm/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_i2c/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_ledc/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_driver_parlio/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/driver/twai/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_phy/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/vfs/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/lwip/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_netif/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/wpa_supplicant/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_wifi/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_adc/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_eth/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_gdbstub/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_psram/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/esp_lcd/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/espcoredump/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/ieee802154/linker.lf;D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/components/openthread/linker.lf --input D:/ESP32_Projects/hello_world/build/esp-idf/esp_system/ld/sections.ld.in --output D:/ESP32_Projects/hello_world/build/esp-idf/esp_system/ld/sections.ld --kconfig D:/esp32-idf-ahy/5.3.2/frameworks/esp-idf-v5.3.2/Kconfig --env-file D:/ESP32_Projects/hello_world/build/config.env --libraries-file D:/ESP32_Projects/hello_world/build/ldgen_libraries --objdump D:/esp32-idf-ahy/5.3.2/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32-elf-objdump.exe || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%